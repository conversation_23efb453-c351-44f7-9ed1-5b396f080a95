#%%
import os
import sys
from pathlib import Path

# from tuning.sell.exp_sell import threshold

current_dir = '/home/<USER>/dev/ta/kaffa_v2'
os.chdir(current_dir)
sys.path.insert(0, current_dir)
outdir = Path('deeplearning/outputs')
(outdir / "models").mkdir(parents=True, exist_ok=True)
(outdir / "metrics").mkdir(exist_ok=True)
(outdir / "predictions").mkdir(exist_ok=True)
#%%
# Load the necessary libraries
import random

random.seed(123)
from joblib import Memory

from core_utils.constant import JOBLIB_CACHE_DIR, REDIS_HOST

from core_utils.redis_cache import EvalRedis
from datetime import timedelta
import pandas as pd
import seaborn as sns
%matplotlib inline
from sklearn.metrics import classification_report
from sklearn.metrics import accuracy_score, f1_score, roc_curve
import warnings
from xgboost.callback import EarlyStopping
from optuna.integration import XGBoostPruningCallback

warnings.simplefilter(action='ignore')
memory = Memory(location=f'{JOBLIB_CACHE_DIR}_tuning', verbose=0)
memory.reduce_size(bytes_limit=3e9, age_limit=timedelta(days=1))
redis_cache = EvalRedis(host=REDIS_HOST, db=1)

exec(open('deeplearning/utils.py', 'r', encoding='utf-8').read())
from deeplearning.utils import *

#%%
def label_strength_4(row, label, thres_2=10, thres_1=0, thres_0=-10):
    """
    Determine the strength of a label based on profit thresholds.
    Args:
        row (pd.Series): A row from the DataFrame containing profit data.
        label (str): The label to evaluate (e.g., '2W', '1M', '3M').
        strong_th (float): Threshold for strong label.
        weak_th (float): Threshold for weak label.
        fail_th (float): Threshold for fail label.

    Returns:
        str: 0:3
        - 0: Fail
        - 1: Weak
        - 2: Strong
        - 3: Very Strong

    """
    v = row[f'profit_{label}']
    if v is None:
        return np.nan
    if v >= thres_2:
        return 3
    elif v >= thres_1:
        return 2
    elif v >= thres_0:
        return 1
    else:
        return 0


def label_strength_3(row, label, strong_th=10, weak_th=0):
    """
    Determine the strength of a label based on profit thresholds.
    Args:
        row (pd.Series): A row from the DataFrame containing profit data.
        label (str): The label to evaluate (e.g., '2W', '1M', '3M').
        strong_th (float): Threshold for strong label.
        weak_th (float): Threshold for weak label.
        fail_th (float): Threshold for fail label.

    Returns:
        str: 0:2
        - 0: Fail
        - 1: Weak
        - 2: Strong

    """
    v = row[f'profit_{label}']
    if v is None:
        return np.nan
    if v >= strong_th:
        return 2
    elif v >= weak_th:
        return 1
    else:
        return 0


def label_binary(row, label, strong_th=5, ceil=True):
    if ceil:
        v = np.ceil(row[f'profit_{label}'])
    else:
        v = row[f'profit_{label}']

    if v is None:
        return np.nan
    if v >= strong_th:
        return 1
    else:
        return 0


def label_binary_v1(row, label, strong_th=5, ceil=True):
    center_dict = {
        '2W': 5,
        '1M': 10,
        '3M': 20
    }
    if ceil:
        v = np.ceil(row[f'profit_{label}_center_{center_dict[label]}'])
    else:
        v = row[f'profit_{label}_center_{center_dict[label]}']

    if v is None:
        return np.nan
    if v >= strong_th:
        return 1
    else:
        return 0


def label_binary_v2(row, label, strong_th=5, ceil=True):
    center_dict = {
        '2W': 3,
        '1M': 7,
        '3M': 15
    }
    if ceil:
        v = np.ceil(row[f'profit_{label}_center_{center_dict[label]}'])
    else:
        v = row[f'profit_{label}_center_{center_dict[label]}']

    if v is None:
        return np.nan
    if v >= strong_th:
        return 1
    else:
        return 0

#%%
def ensure_types(df, labels_tag):
    df = df.copy()
    for c in df.columns:
        if c in labels_tag:
            continue
        if pd.api.types.is_numeric_dtype(df[c]):
            df[c] = df[c].astype(np.float32)
    return df


chunks = []
for chunk in pd.read_csv("deeplearning/dl_train.csv", chunksize=50000):
    chunks.append(chunk)
df_data_all = pd.concat(chunks, ignore_index=True)

# df_data_all = pd.read_parquet(path_parquet)
profit_cols = [col for col in df_data_all.columns if col.startswith('profit_')]
cname_tvt = 'tvt'

df_data_all['week'] = pd.to_datetime(df_data_all['time']).dt.strftime('%Y-%W')
df_data_all['month'] = df_data_all['time'].map(lambda x: x[:7])
labels_tag = [cname_tvt, 'time', 'ticker', 'week', 'month'] + profit_cols
df_data_all = ensure_types(df_data_all, labels_tag)

df_data_all = df_data_all.sort_values(by=['ticker', 'time']).reset_index(drop=True)

# Load data and labels
thres_hold = 10

# for label in ['2W', '1M', '3M']:
for label in ['1M']:
    df_data_all[f'label_binary_{label}_ceil'] = df_data_all.apply(
        lambda row: label_binary(row, label, strong_th=thres_hold),
        axis=1)
    labels_tag.append(f'label_binary_{label}_ceil')

    df_data_all[f'label_binary_{label}_center_v1_ceil'] = df_data_all.apply(
        lambda row: label_binary_v1(row, label, strong_th=thres_hold), axis=1)
    labels_tag.append(f'label_binary_{label}_center_v1_ceil')

    df_data_all[f'label_binary_{label}_center_v2_ceil'] = df_data_all.apply(
        lambda row: label_binary_v2(row, label, strong_th=thres_hold), axis=1)
    labels_tag.append(f'label_binary_{label}_center_v2_ceil')

    df_data_all[f'label_binary_{label}'] = df_data_all.apply(
        lambda row: label_binary(row, label, strong_th=thres_hold, ceil=False),
        axis=1)
    labels_tag.append(f'label_binary_{label}')

    df_data_all[f'label_binary_{label}_center_v1'] = df_data_all.apply(
        lambda row: label_binary_v1(row, label, strong_th=thres_hold, ceil=False), axis=1)
    labels_tag.append(f'label_binary_{label}_center_v1')

    df_data_all[f'label_binary_{label}_center_v2'] = df_data_all.apply(
        lambda row: label_binary_v2(row, label, strong_th=thres_hold, ceil=False), axis=1)
    labels_tag.append(f'label_binary_{label}_center_v2')

    # # multi 3
    # df_data_all[f'label_{label}_8'] = df_data_all.apply(lambda row: label_strength_3(row, label, strong_th=8), axis=1)
    # labels_tag.append(f'label_{label}_8')
    # df_data_all[f'label_{label}_12'] = df_data_all.apply(lambda row: label_strength_3(row, label,strong_th=12), axis=1)
    # labels_tag.append(f'label_{label}_12')
    # df_data_all[f'label_{label}_26'] = df_data_all.apply(lambda row: label_strength_3(row, label, strong_th=26), axis=1)
    # labels_tag.append(f'label_{label}_26')
    # #multi 4
    # df_data_all[f'label_{label}_-6_1_9'] = df_data_all.apply(lambda row: label_strength_4(row, label, thres_2=9, thres_1=1, thres_0=-6), axis=1)
    # labels_tag.append(f'label_{label}_-6_1_9')
    # df_data_all[f'label_{label}_-9_2_15'] = df_data_all.apply(lambda row: label_strength_4(row, label,thres_2=15, thres_1=2, thres_0=-9), axis=1)
    # labels_tag.append(f'label_{label}_-9_2_15')
    # df_data_all[f'label_{label}_-15_7_33'] = df_data_all.apply(lambda row: label_strength_4(row, label, thres_2=33, thres_1=2, thres_0=-9), axis=1)
    # labels_tag.append(f'label_{label}_-15_7_33')


# drops_1 = ['MA20_T1', 'MA200_T1', 'MA10', 'D_RSI_Max1W_MACD', 'D_CMB', 'Close_T1W', 'D_MACDdiff', 'D_CMB_XFast', 'D_RSI/D_RSI_T1', 'D_RSI/D_RSI_T1W']
# labels_tag = labels_tag + drops
# df_data_all.drop_duplicates(subset=['week', 'ticker'], keep='first', inplace=True)
#%%
print("Data shape:", df_data_all.shape)
data_is_null = df_data_all.isnull()
mean_null = data_is_null.mean()

drop_cols = []
for col in df_data_all.columns:
    if mean_null[col] > 0.15 and col not in labels_tag:
        drop_cols.append(col)

df_data_all.drop(columns=drop_cols, inplace=True)
print("Data shape:", df_data_all.shape)

#%%
df_data_all = df_data_all.drop(drop_cols, axis=1)
df_data_all = df_data_all.replace([np.inf, -np.inf], np.nan).dropna()

df_data_all = df_data_all.dropna(axis=0, how='any')
df_data_all = split_tvt(df_data_all, test_size=10, time_col='time', train_cutoff='2022-06-01', cal_cutoff='2023-01-01',
                        val_cutoff='2023-06-01')


#%%
df_data = df_data_all.drop_duplicates(subset=['month', 'ticker'], keep='first').copy()
# df_data = df_data_all.drop_duplicates(subset=['week', 'ticker'], keep='first').copy()
# df_data = df_data_all.copy()
df_data.to_csv('deeplearning/dl_train_used.csv', index=False)
#%% md

#%%
data_train, data_test1, data_test2, data_cal, data_val = (
    df_data[df_data[cname_tvt] == 'train'].reset_index(drop=True), \
    df_data[df_data[cname_tvt] == 'test1'].reset_index(drop=True),
    df_data[df_data[cname_tvt] == 'test2'].reset_index(drop=True),
    df_data[df_data[cname_tvt] == 'cal'].reset_index(drop=True),
    df_data[df_data[cname_tvt] == 'val'].reset_index(drop=True)
)
y_train_m, y_test1_m, y_test2_m, y_cal_m, y_val_m = data_train[labels_tag], data_test1[labels_tag], data_test2[
    labels_tag], data_cal[labels_tag], data_val[labels_tag]

X_train, X_test1, X_test2, X_cal, X_val = (
    data_train.drop(labels_tag, axis=1),
    data_test1.drop(labels_tag, axis=1),
    data_test2.drop(labels_tag, axis=1),
    data_cal.drop(labels_tag, axis=1),
    data_val.drop(labels_tag, axis=1)
)
X_train_final = X_train
X_val_final = X_val
X_cal_final = X_cal
X_test1_final = X_test1
X_test2_final = X_test2

print(X_train_final.shape, X_val_final.shape, X_cal_final.shape, X_test1_final.shape, X_test2_final.shape)


# Normalize
# scaler = StandardScaler()
# scaler.fit(X_train)

# X_train_scaled = scaler.transform(X_train)
# X_cal_scaled = scaler.transform(X_cal)
# X_val_scaled = scaler.transform(X_val)
# X_test1_scaled = scaler.transform(X_test1)
# X_test2_scaled = scaler.transform(X_test2)

# X_train_final = X_train_scaled
# X_val_final = X_val_scaled
# X_cal_final = X_cal_scaled
# X_test1_final = X_test1_scaled
# X_test2_final = X_test2_scaled
#%%
# ==== HPO với Optuna cho XGBoost + metric không cần threshold ====
import optuna
from optuna.exceptions import TrialPruned
from optuna.samplers import TPESampler
from optuna.pruners import MedianPruner
from optuna.integration import XGBoostPruningCallback
from xgboost.callback import EarlyStopping

import xgboost as xgb  # <-- nhớ import
from sklearn.metrics import (
    average_precision_score, roc_auc_score, log_loss, brier_score_loss
)

METRIC_MODE = "ap"  # "ap" | "roc_auc" | "neg_logloss" | "neg_brier" | "combo"

def prob_metric(y_true, proba, mode="ap"):
    if mode == 'ap':
        return average_precision_score(y_true, proba)
    if mode == 'roc_auc':
        return roc_auc_score(y_true, proba)
    if mode == 'neg_logloss':
        return -log_loss(y_true, proba)
    if mode == 'neg_brier':
        return -brier_score_loss(y_true, proba)
    if mode == 'combo':
        ap = average_precision_score(y_true, proba)
        roc = roc_auc_score(y_true, proba)
        return 0.7 * ap + 0.3 * roc
    raise ValueError("METRIC_MODE không hợp lệ")


label_list_mc = ['label_binary_1M', 'label_binary_1M_center_v1', 'label_binary_1M_center_v2']

for lb in label_list_mc:
    print('total data distribution', df_data[f'{lb}'].value_counts())

    y_train = y_train_m[lb]
    y_val   = y_val_m[lb]
    y_cal   = y_cal_m[lb]
    y_test1 = y_test1_m[lb]
    y_test2 = y_test2_m[lb]

    print(f"Train {lb} distribution:", np.bincount(y_train))
    sns.countplot(x=y_train)
    plt.title("Label distribution")
    plt.show()

    # ======= sample_weight (imbalance) =======
    sample_weight = compute_sample_weight(class_weight='balanced', y=y_train)

    # ======= Optuna objective (DÙNG xgb.train để gắn callbacks) =======
    def objective(trial: optuna.trial.Trial):
        lr_grid  = [0.003, 0.005, 0.007, 0.01, 0.015, 0.02, 0.03, 0.05, 0.07, 0.10, 0.15, 0.20, 0.30]
        reg_grid = [1e-8, 1e-6, 1e-4, 1e-3, 0.01, 0.03, 0.1, 0.3, 1, 3, 10]
        mcw_grid = [1, 1.5, 2, 3, 4, 5, 7, 10]

        params = {
            "objective": "binary:logistic",
            "tree_method": trial.suggest_categorical("tree_method", ["hist"]),
            "learning_rate": trial.suggest_categorical("learning_rate", lr_grid),
            "max_depth": trial.suggest_int("max_depth", 3, 10, step=1),
            "min_child_weight": float(trial.suggest_categorical("min_child_weight", mcw_grid)),
            "subsample": trial.suggest_float("subsample", 0.55, 0.95, step=0.05),
            "colsample_bytree": trial.suggest_float("colsample_bytree", 0.55, 0.95, step=0.05),
            "reg_alpha": float(trial.suggest_categorical("reg_alpha", reg_grid)),
            "reg_lambda": float(trial.suggest_categorical("reg_lambda", reg_grid)),
            "max_bin": trial.suggest_categorical("max_bin", [128, 256, 512]),
            "gamma": float(trial.suggest_categorical("gamma", [0, 0.5, 1, 2, 3, 5, 7, 10])),

            # train params
            "eval_metric": "logloss",
            "seed": 42,
            "nthread": 20,   # cho xgb.train
            "verbosity": 0,
        }

        # DMatrix để gắn weight
        dtrain = xgb.DMatrix(X_train_final, label=y_train, weight=sample_weight)
        dval   = xgb.DMatrix(X_val_final,   label=y_val)
        evallist = [(dtrain, "train"), (dval, "validation_0")]

        bst = xgb.train(
            params=params,
            dtrain=dtrain,
            num_boost_round=10000,  # n_estimators lớn + early stopping cắt
            evals=evallist,
            callbacks=[
                XGBoostPruningCallback(trial, "validation_0-logloss"),
                EarlyStopping(rounds=200, save_best=True)
            ],
            verbose_eval=False
        )

        # predict proba cho VAL, chấm điểm theo METRIC_MODE
        proba_val = bst.predict(dval)
        score = prob_metric(y_val, proba_val, METRIC_MODE)
        return float(score)

    study = optuna.create_study(
        direction="maximize",
        sampler=TPESampler(seed=42),
        pruner=MedianPruner(n_warmup_steps=5)
    )
    study.optimize(objective, n_trials=200, n_jobs=20, show_progress_bar=True)

    print("\nBest score:", study.best_value)
    print("Best params:", study.best_trial.params)

    # ======= Train final với best params (sklearn API) =======
    best_params = study.best_trial.params.copy()
    best_params.update({
        'objective': 'binary:logistic',
        'n_estimators': 10000,
        'eval_metric': 'logloss',
        'random_state': 42,
        'n_jobs': 20,
        'verbosity': 0,
        'early_stopping_rounds':200
    })

    # Lưu ý: KHÔNG truyền callbacks vào XGBClassifier.fit()
    final_model = xgb.XGBClassifier(**best_params)
    final_model.fit(
        X_train_final, y_train,
        sample_weight=sample_weight,
        eval_set=[(X_val_final, y_val)],
        verbose=False
    )

    # ======= Đánh giá trên TRAIN/VAL (proba metrics, no threshold) =======
    print('=' * 80, f'[{lb}] TRAIN/VAL EVALUATION (proba metrics)', '=' * 80)
    for X_, y_, tag in [
        (X_train_final, y_train, "TRAIN"),
        (X_val_final, y_val, "VAL")
    ]:
        p_ = final_model.predict_proba(X_)[:, 1]
        ap = average_precision_score(y_, p_)
        auc = roc_auc_score(y_, p_)
        nll = -log_loss(y_, p_)
        nbri = -brier_score_loss(y_, p_)
        print(f"{tag}: AP={ap:.4f} | AUC={auc:.4f} | -LogLoss={nll:.4f} | -Brier={nbri:.4f}")

    # ======= Báo cáo phân phối + AUC (như cũ) =======
    base = len(y_train)
    target = y_train.sum()
    percent_target = 100 * target / base
    tag = '2M' if '2M' in lb else '3M' if '3M' in lb else '1M'
    auc_train = roc_auc_score(y_train, final_model.predict_proba(X_train_final)[:, 1])
    report = [{
        "Threshold": f"{lb}",
        "Base": f"{base / 1000:.0f}K",
        "Target": f"{target / 1000:.0f}K",
        "%target": f"{percent_target:.0f}%",
        "AUC": f"{auc_train * 100:.0f}%"
    }]
    report_df = pd.DataFrame(report)
    print(report_df.to_markdown(index=False))

    report_lift_table(data_train, y_train, final_model.predict_proba(X_train_final)[:, 1], profit_col=f'profit_{tag}')

    # ======= Feature importance + lưu model =======
    imp = feature_importance_df(final_model)
    joblib.dump(final_model, outdir / "models" / f"xgb_model_{lb}.joblib")
    final_model.get_booster().save_model(str(outdir / "models" / f"xgb_model_{lb}.json"))
    imp.to_csv(outdir / "models" / f"feature_importance_{lb}.csv", index=False)

    # ======= Calibration (sigmoid) trên CAL set =======
    print(f"[INFO] Calibrate ({lb})...")
    calibrator = CalibratedClassifierCV(final_model, method="sigmoid", cv="prefit")
    calibrator.fit(X_cal, y_cal)
    joblib.dump(calibrator, outdir / "models" / f"calibrator_{lb}.joblib")

    print("++++++++++++++++++++++++ TEST EVALUATION (no threshold) ++++++++++++++++++++++++")
    for X_test_final, y_test, data_test, tag_eval in [
        (X_test1_final, y_test1, data_test1, "TEST1"),
        (X_test2_final, y_test2, data_test2, "TEST2")
    ]:
        # p_test = calibrator.predict_proba(X_test_final)[:, 1]  # nếu muốn dùng calibrate
        p_test = final_model.predict_proba(X_test_final)[:, 1]

        ap = average_precision_score(y_test, p_test)
        auc = roc_auc_score(y_test, p_test)
        nll = -log_loss(y_test, p_test)
        nbri = -brier_score_loss(y_test, p_test)

        ks, ks_threshold = ks_score(y_test, p_test)
        print(f"{tag_eval}: AP={ap:.4f} | AUC={auc:.4f} | -LogLoss={nll:.4f} | -Brier={nbri:.4f} | KS={ks:.4f} @thr={ks_threshold:.4f}")

        base = len(y_test); target = y_test.sum(); percent_target = 100 * target / base
        rep = [{"Threshold": f"{lb}", "Base": f"{base / 1000:.0f}K", "Target": f"{target / 1000:.0f}K",
                "%target": f"{percent_target:.0f}%", "AUC": f"{auc * 100:.0f}%"}]
        print(pd.DataFrame(rep).to_markdown(index=False))

        report_lift_table(data_test, y_test, p_test, profit_col=f'profit_{tag}')

#%%
# ==== HPO với Optuna cho XGBoost + metric không cần threshold ====
import optuna
from optuna.exceptions import TrialPruned
from optuna.samplers import TPESampler
from optuna.pruners import MedianPruner
from optuna.integration import XGBoostPruningCallback
from xgboost.callback import EarlyStopping

import xgboost as xgb  # <-- nhớ import
from sklearn.metrics import (
    average_precision_score, roc_auc_score, log_loss, brier_score_loss
)

METRIC_MODE = "ap"  # "ap" | "roc_auc" | "neg_logloss" | "neg_brier" | "combo"

def prob_metric(y_true, proba, mode="ap"):
    if mode == 'ap':
        return average_precision_score(y_true, proba)
    if mode == 'roc_auc':
        return roc_auc_score(y_true, proba)
    if mode == 'neg_logloss':
        return -log_loss(y_true, proba)
    if mode == 'neg_brier':
        return -brier_score_loss(y_true, proba)
    if mode == 'combo':
        ap = average_precision_score(y_true, proba)
        roc = roc_auc_score(y_true, proba)
        return 0.7 * ap + 0.3 * roc
    raise ValueError("METRIC_MODE không hợp lệ")


label_list_mc = ['label_binary_1M', 'label_binary_1M_center_v1', 'label_binary_1M_center_v2']

for lb in label_list_mc:
    print(f"{'_--'*50}")
    print('total data distribution', df_data[f'{lb}'].value_counts())

    y_train = y_train_m[lb]
    y_val   = y_val_m[lb]
    y_cal   = y_cal_m[lb]
    y_test1 = y_test1_m[lb]
    y_test2 = y_test2_m[lb]

    print(f"Train {lb} distribution:", np.bincount(y_train))
    sns.countplot(x=y_train)
    plt.title("Label distribution")
    plt.show()

    # ======= sample_weight (imbalance) =======
    sample_weight = compute_sample_weight(class_weight='balanced', y=y_train)

    # ======= Optuna objective (DÙNG xgb.train để gắn callbacks) =======


    print("\nBest score:", study.best_value)
    print("Best params:", study.best_trial.params)

    # ======= Train final với best params (sklearn API) =======
    best_params = study.best_trial.params.copy()
    best_params.update({
        'objective': 'binary:logistic',
        'n_estimators': 10000,
        'eval_metric': 'logloss',
        'random_state': 42,
        'n_jobs': 20,
        'verbosity': 0,
        'early_stopping_rounds':200
    })

    # Lưu ý: KHÔNG truyền callbacks vào XGBClassifier.fit()
    final_model = xgb.XGBClassifier(**best_params)
    final_model.fit(
        X_train_final, y_train,
        sample_weight=sample_weight,
        eval_set=[(X_val_final, y_val)],
        verbose=False
    )


    # ======= Đánh giá trên TRAIN/VAL (proba metrics, no threshold) =======
    print('=' * 80, f'[{lb}] TRAIN/VAL EVALUATION (proba metrics)', '=' * 80)
    for X_, y_, tag in [
        (X_train_final, y_train, "TRAIN"),
        (X_val_final, y_val, "VAL")
    ]:
        p_ = final_model.predict_proba(X_)[:, 1]
        ap = average_precision_score(y_, p_)
        auc = roc_auc_score(y_, p_)
        nll = -log_loss(y_, p_)
        nbri = -brier_score_loss(y_, p_)
        print(f"{tag}: AP={ap:.4f} | AUC={auc:.4f} | -LogLoss={nll:.4f} | -Brier={nbri:.4f}")

    # ======= Báo cáo phân phối + AUC (như cũ) =======
    base = len(y_train)
    target = y_train.sum()
    percent_target = 100 * target / base
    tag = '2M' if '2M' in lb else '3M' if '3M' in lb else '1M'
    auc_train = roc_auc_score(y_train, final_model.predict_proba(X_train_final)[:, 1])
    report = [{
        "Threshold": f"{lb}",
        "Base": f"{base / 1000:.0f}K",
        "Target": f"{target / 1000:.0f}K",
        "%target": f"{percent_target:.0f}%",
        "AUC": f"{auc_train * 100:.0f}%"
    }]
    report_df = pd.DataFrame(report)
    print(report_df.to_markdown(index=False))

    report_lift_table(data_train, y_train, final_model.predict_proba(X_train_final)[:, 1], profit_col=f'profit_{tag}')

    # ======= Feature importance + lưu model =======
    imp = feature_importance_df(final_model)
    joblib.dump(final_model, outdir / "models" / f"xgb_model_{lb}.joblib")
    final_model.get_booster().save_model(str(outdir / "models" / f"xgb_model_{lb}.json"))
    imp.to_csv(outdir / "models" / f"feature_importance_{lb}.csv", index=False)

    # ======= Calibration (sigmoid) trên CAL set =======
    print(f"[INFO] Calibrate ({lb})...")
    calibrator = CalibratedClassifierCV(final_model, method="sigmoid", cv="prefit")
    calibrator.fit(X_cal, y_cal)
    joblib.dump(calibrator, outdir / "models" / f"calibrator_{lb}.joblib")

    print("++++++++++++++++++++++++ TEST EVALUATION (no threshold) ++++++++++++++++++++++++")
    for X_test_final, y_test, data_test, tag_eval in [
        (X_test1_final, y_test1, data_test1, "TEST1"),
        (X_test2_final, y_test2, data_test2, "TEST2")
    ]:
        # p_test = calibrator.predict_proba(X_test_final)[:, 1]  # nếu muốn dùng calibrate
        p_test = final_model.predict_proba(X_test_final)[:, 1]

        ap = average_precision_score(y_test, p_test)
        auc = roc_auc_score(y_test, p_test)
        nll = -log_loss(y_test, p_test)
        nbri = -brier_score_loss(y_test, p_test)

        ks, ks_threshold = ks_score(y_test, p_test)
        print(f"{tag_eval}: AP={ap:.4f} | AUC={auc:.4f} | -LogLoss={nll:.4f} | -Brier={nbri:.4f} | KS={ks:.4f} @thr={ks_threshold:.4f}")

        base = len(y_test); target = y_test.sum(); percent_target = 100 * target / base
        rep = [{"Threshold": f"{lb}", "Base": f"{base / 1000:.0f}K", "Target": f"{target / 1000:.0f}K",
                "%target": f"{percent_target:.0f}%", "AUC": f"{auc * 100:.0f}%"}]
        print(pd.DataFrame(rep).to_markdown(index=False))

        report_lift_table(data_test, y_test, p_test, profit_col=f'profit_{tag}')

#%%
# 1) Hai pha HPO → HPO refine (coarse → fine)
#
# Mục tiêu: khoanh vùng nhanh, rồi mài kỹ trên window gần hiện tại.
# Dùng khi: dữ liệu dài nhiều năm, thị trường đổi regime.
#
# Stage 1 – Coarse (history dài):
#
# Train/val: dùng window dài hơn (ví dụ 2014–2021 train, 2022 val).
#
# Optuna TPE + pruning, space rộng (eta, depth, subsample, colsample, reg, gamma, max_bin).
#
# 100–200 trials để tìm vùng tốt (không phải điểm tốt nhất).
#
# Stage 2 – Fine (recent regime):
#
# Train/val: recent (ví dụ 2018–2022 train, 2023 val) hoặc rolling gần đây.
#
# Thu hẹp space quanh best params stage 1 (± delta nhỏ), tập trung vài tham số nhạy: learning_rate, reg_alpha/lambda, subsample/colsample, đôi khi max_depth.
#
# 50–150 trials tìm best hiện tại.
#
# Final:
#
# Train lại trên train_recent (gộp val_recent nếu muốn) + early stopping bằng 1 holdout nhỏ hoặc CV theo time-series.
#
# Calibrate trên cal như code của bạn.
#
# Đánh giá test1/test2.
#
# Ghép vào code:
#
# Giữ nguyên hàm Optuna ở trên. Chạy study1 = ... với (X_train_hist, y_train_hist, X_val_hist, y_val_hist).
#
# Lấy p1 = study1.best_trial.params. Tạo space stage 2: mỗi tham số đặt quanh p1 (ví dụ learning_rate ± 2–3x theo log-scale, max_depth ±1, etc.).
#
# Chạy study2 trên (X_train_recent, X_val_recent). Dùng study2.best_trial.params cho training final.
#%%
# # giả sử đã có X_train_hist, y_train_hist, X_val_hist, y_val_hist
# study1 = run_optuna_on_split(X_train_hist, y_train_hist, X_val_hist, y_val_hist, space="wide", n_trials=200)
#
# p1 = study1.best_trial.params
# def narrow_space(trial, base):
#     return {
#         "tree_method": trial.suggest_categorical("tree_method", [base["tree_method"]]),
#         "learning_rate": trial.suggest_float(
#             "learning_rate",
#             max(1e-3, base["learning_rate"]/3),
#             min(0.3,  base["learning_rate"]*3),
#             log=True
#         ),
#         "max_depth": trial.suggest_int("max_depth", max(3, base["max_depth"]-1), min(12, base["max_depth"]+1)),
#         "min_child_weight": trial.suggest_float("min_child_weight",
#             max(1.0, base["min_child_weight"]/2), min(10.0, base["min_child_weight"]*2), log=True),
#         "subsample": trial.suggest_float("subsample",
#             max(0.5, base["subsample"]-0.2), min(1.0, base["subsample"]+0.2)),
#         "colsample_bytree": trial.suggest_float("colsample_bytree",
#             max(0.5, base["colsample_bytree"]-0.2), min(1.0, base["colsample_bytree"]+0.2)),
#         "reg_alpha": trial.suggest_float("reg_alpha",
#             max(1e-8, base["reg_alpha"]/5), min(10.0, base["reg_alpha"]*5), log=True),
#         "reg_lambda": trial.suggest_float("reg_lambda",
#             max(1e-8, base["reg_lambda"]/5), min(10.0, base["reg_lambda"]*5), log=True),
#         "gamma": trial.suggest_float("gamma",
#             max(0.0, base["gamma"]-2.0), min(10.0, base["gamma"]+2.0)),
#         "max_bin": trial.suggest_categorical("max_bin", [128, 256, 512])
#     }
#
# # chạy stage 2 trên recent
# study2 = run_optuna_on_split(
#     X_train_recent, y_train_recent, X_val_recent, y_val_recent,
#     space=("narrow", p1, narrow_space), n_trials=120
# )
#
# best_params = study2.best_trial.params
# # train final trên recent (+ early stopping), calibrate, test như bạn đang làm

#%%
def flow1_coarse_fine(
    X_hist_tr, y_hist_tr, X_hist_va, y_hist_va,   # history window (dài)
    X_recent_tr, y_recent_tr, X_recent_va, y_recent_va,  # recent window (gần hiện tại)
    X_cal, y_cal,
    X_tests, y_tests, data_tests, test_tags,
    label_name, outdir,
    metric_mode="ap",
    n_trials_stage1=200,
    n_trials_stage2=120,
    use_calibrated=True
):
    # weight theo history (dùng cho HPO stage 1)
    sw_hist = compute_sample_weight('balanced', y_hist_tr)

    # === Stage 1: HPO space rộng trên history ===
    study1 = run_optuna_on_split(
        X_hist_tr, y_hist_tr, X_hist_va, y_hist_va,
        metric_mode=metric_mode, n_trials=n_trials_stage1, n_jobs=8,
        space="wide", sample_weight=sw_hist
    )
    p1 = study1.best_trial.params
    print("[Stage1] best params:", p1)

    # === Stage 2: HPO space hẹp quanh best params trên recent ===
    sw_recent = compute_sample_weight('balanced', y_recent_tr)
    study2 = run_optuna_on_split(
        X_recent_tr, y_recent_tr, X_recent_va, y_recent_va,
        metric_mode=metric_mode, n_trials=n_trials_stage2, n_jobs=8,
        space=("narrow", p1, default_narrow_space),
        sample_weight=sw_recent
    )
    best_params = study2.best_trial.params
    print("[Stage2] best params:", best_params)

    # === Train final trên recent + eval + save ===
    model = train_eval_save(
        best_params,
        X_recent_tr, y_recent_tr, X_recent_va, y_recent_va,
        X_cal, y_cal,
        X_tests, y_tests, data_tests, test_tags,
        label_name, outdir,
        metric_mode=metric_mode,
        use_calibrated=use_calibrated
    )
    return {"study1": study1, "study2": study2, "model": model}

# ví dụ tách train thành history & recent theo thời gian của team
# X_hist_tr, y_hist_tr, X_hist_va, y_hist_va = ...
# X_recent_tr, y_recent_tr, X_recent_va, y_recent_va = ...

res1 = flow1_coarse_fine(
    X_hist_tr, y_hist_tr, X_hist_va, y_hist_va,
    X_train_final, y_train, X_val_final, y_val,     # recent = train/val hiện tại
    X_cal, y_cal,
    [X_test1_final, X_test2_final], [y_test1, y_test2], [data_test1, data_test2], ["TEST1","TEST2"],
    label_name=lb, outdir=outdir,
    metric_mode="ap", n_trials_stage1=200, n_trials_stage2=120, use_calibrated=True
)
#%%
# 2) Hai pha Fit tham số → Fine-tune theo thời gian (regime-aware)
#
# Mục tiêu: học “hình dạng” model từ lịch sử, sau đó tái khớp nhẹ theo dữ liệu mới.
# Dùng khi: shift mạnh, muốn ưu tiên recent mà vẫn giữ kiến thức cũ.
#
# Stage 1 – Fit tham số tổng quát (history dài):
#
# Chạy Optuna (space rộng) để chọn hyperparams ổn định.
#
# Train model với n_estimators lớn + early stopping.
#
# Stage 2 – Fine-tune recent:
#
# Option A (phổ biến, sạch): giữ nguyên hyperparams stage 1, re-tune rất ít tham số trên recent: learning_rate, reg_alpha, reg_lambda, subsample/colsample — hoặc chỉ giảm learning_rate rồi train lại từ đầu trên recent (không dùng threshold).
#
# Option B (tiếp tục boosting): dùng xgb_model=final_stage1.get_booster() để append thêm cây trên recent (sklearn API hỗ trợ). Phù hợp khi muốn “thêm trí nhớ” recent, nhưng cẩn thận overfit.
#
# model2 = xgb.XGBClassifier(**params_stage1, n_estimators=best_ntree + 200)
# model2.fit(X_recent, y_recent, xgb_model=final_stage1.get_booster(),
#            eval_set=[(X_val_recent, y_val_recent)],
#            early_stopping_rounds=100, verbose=False)
#
#
# Giữ cal cho calibration sau fine-tune.
#
# Final: dùng model sau Stage 2 để predict, rồi calibrate (Platt/Isotonic) trên cal.
#%%
# Option A (khuyên dùng): re-tune nhẹ vài tham số trên recent, train từ đầu.
def flow2_finetune_A(
    X_hist_tr, y_hist_tr, X_hist_va, y_hist_va,  # stage 1: history
    X_recent_tr, y_recent_tr, X_recent_va, y_recent_va,  # stage 2: recent
    X_cal, y_cal,
    X_tests, y_tests, data_tests, test_tags,
    label_name, outdir,
    metric_mode="ap",
    n_trials_stage1=200, n_trials_stage2=80,
    use_calibrated=True
):
    # Stage 1: HPO rộng trên history → params ổn định
    sw_hist = compute_sample_weight('balanced', y_hist_tr)
    study1 = run_optuna_on_split(
        X_hist_tr, y_hist_tr, X_hist_va, y_hist_va,
        metric_mode=metric_mode, n_trials=n_trials_stage1, n_jobs=8,
        space="wide", sample_weight=sw_hist
    )
    base_params = study1.best_trial.params
    print("[Stage1] base params:", base_params)

    # Stage 2: chỉ re-tune một nhóm nhỏ tham số trên recent
    def narrow_fn_small(trial, base):
        return {
            "tree_method": trial.suggest_categorical("tree_method", [base.get("tree_method","hist")]),
            "learning_rate": trial.suggest_float("learning_rate",
                max(1e-3, base["learning_rate"]/3), min(0.1, base["learning_rate"]*2), log=True),
            "subsample": trial.suggest_float("subsample",
                max(0.5, base["subsample"]-0.15), min(1.0, base["subsample"]+0.15)),
            "colsample_bytree": trial.suggest_float("colsample_bytree",
                max(0.5, base["colsample_bytree"]-0.15), min(1.0, base["colsample_bytree"]+0.15)),
            "reg_alpha": trial.suggest_float("reg_alpha",
                max(1e-8, base["reg_alpha"]/3), min(10.0, base["reg_alpha"]*3), log=True),
            "reg_lambda": trial.suggest_float("reg_lambda",
                max(1e-8, base["reg_lambda"]/3), min(10.0, base["reg_lambda"]*3), log=True),
            "max_depth": trial.suggest_int("max_depth",
                max(3, base["max_depth"]-1), min(12, base["max_depth"]+1)),
            "min_child_weight": trial.suggest_float("min_child_weight",
                max(1.0, base["min_child_weight"]/2), min(10.0, base["min_child_weight"]*2), log=True),
            "gamma": trial.suggest_float("gamma",
                max(0.0, base["gamma"]-1.0), min(10.0, base["gamma"]+1.0)),
            "max_bin": trial.suggest_categorical("max_bin", [128, 256, 512])
        }

    sw_recent = compute_sample_weight('balanced', y_recent_tr)
    study2 = run_optuna_on_split(
        X_recent_tr, y_recent_tr, X_recent_va, y_recent_va,
        metric_mode=metric_mode, n_trials=n_trials_stage2, n_jobs=8,
        space=("narrow", base_params, narrow_fn_small),
        sample_weight=sw_recent
    )
    best_params = study2.best_trial.params
    print("[Stage2] tuned params:", best_params)

    # Train final trên recent + eval + save
    model = train_eval_save(
        best_params,
        X_recent_tr, y_recent_tr, X_recent_va, y_recent_va,
        X_cal, y_cal,
        X_tests, y_tests, data_tests, test_tags,
        label_name, outdir,
        metric_mode=metric_mode,
        use_calibrated=use_calibrated
    )
    return {"study1": study1, "study2": study2, "model": model}

#%%
# Option B (nâng cấp dần – cẩn thận overfit): tiếp tục boosting từ model stage 1 trên dữ liệu recent.
def flow2_finetune_B_continue_boosting(
    X_hist_tr, y_hist_tr, X_hist_va, y_hist_va,
    X_recent_tr, y_recent_tr, X_recent_va, y_recent_va,
    X_cal, y_cal,
    X_tests, y_tests, data_tests, test_tags,
    label_name, outdir,
    metric_mode="ap",
    n_trials_stage1=200,
    extra_trees=300,
    use_calibrated=True
):
    # Stage 1: HPO rộng → train final 1
    sw_hist = compute_sample_weight('balanced', y_hist_tr)
    study1 = run_optuna_on_split(
        X_hist_tr, y_hist_tr, X_hist_va, y_hist_va,
        metric_mode=metric_mode, n_trials=n_trials_stage1, n_jobs=8,
        space="wide", sample_weight=sw_hist
    )
    p1 = study1.best_trial.params.copy()
    p1.update({'objective':'binary:logistic','n_estimators':10000,'eval_metric':'logloss','random_state':42,'n_jobs':-1})
    model1 = xgb.XGBClassifier(**p1)
    model1.fit(X_hist_tr, y_hist_tr, eval_set=[(X_hist_va, y_hist_va)], early_stopping_rounds=200, verbose=False)
    best_ntree = model1.best_ntree_limit

    # Stage 2: thêm cây trên recent
    p2 = p1.copy()
    p2['n_estimators'] = int(best_ntree + extra_trees)
    model2 = xgb.XGBClassifier(**p2)
    model2.fit(
        X_recent_tr, y_recent_tr,
        xgb_model=model1.get_booster(),  # tiếp tục từ model1
        eval_set=[(X_recent_va, y_recent_va)],
        early_stopping_rounds=100,
        verbose=False
    )

    # Calibration + test
    if use_calibrated:
        calibrator = CalibratedClassifierCV(model2, method="sigmoid", cv="prefit")
        calibrator.fit(X_cal, y_cal)
        pred_fn = lambda X: calibrator.predict_proba(X)[:, 1]
        joblib.dump(calibrator, outdir / "models" / f"calibrator_{label_name}.joblib")
    else:
        pred_fn = lambda X: model2.predict_proba(X)[:, 1]

    for tag, X_, y_ in [("TRAIN_RECENT", X_recent_tr, y_recent_tr), ("VAL_RECENT", X_recent_va, y_recent_va)]:
        p_ = pred_fn(X_)
        print(f"[{label_name}] {tag}: AP={average_precision_score(y_, p_):.4f} | "
              f"AUC={roc_auc_score(y_, p_):.4f} | -LogLoss={-log_loss(y_, p_):.4f} | "
              f"-Brier={-brier_score_loss(y_, p_):.4f}")

    joblib.dump(model2, outdir / "models" / f"xgb_model_{label_name}.joblib")
    model2.get_booster().save_model(str(outdir / "models" / f"xgb_model_{label_name}.json"))

    period_tag = '2M' if '2M' in label_name else '3M' if '3M' in label_name else '1M'
    for X_te, y_te, data_te, tg in zip(X_tests, y_tests, data_tests, test_tags):
        p_te = pred_fn(X_te)
        ap  = average_precision_score(y_te, p_te)
        auc = roc_auc_score(y_te, p_te)
        nll = -log_loss(y_te, p_te)
        bri = -brier_score_loss(y_te, p_te)
        print(f"[{label_name}] {tg}: AP={ap:.4f} | AUC={auc:.4f} | -LogLoss={nll:.4f} | -Brier={bri:.4f}")
        report_lift_table(data_te, y_te, p_te, profit_col=f'profit_{period_tag}')

    return {"study1": study1, "model2": model2}

#%%

#%%

#%%
