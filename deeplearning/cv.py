# ==========================================================
# Walk-Forward + Purge/Embargo + 2-stage + Metrics (Trading)
# ==========================================================

"""
This module implements a walk-forward evaluation framework for time-series data,
with features tailored for trading strategies. It includes functions for creating
time-aware cross-validation splits, applying purge/embargo logic, training models
with sample weights, and evaluating trading-specific metrics.

Key Features:
- Walk-forward validation with purge/embargo logic.
- Two-stage model training with custom sample weighting.
- Trading-specific metrics like IC, lift, and Sharpe ratio.
- Support for time-decay, uniqueness, cost-aware, and focal-like sample weights.
"""
import numpy as np
import pandas as pd
from dataclasses import dataclass
from typing import List, Dict, Any, Tuple, Optional
from scipy.stats import spearmanr

from sklearn.metrics import average_precision_score, roc_auc_score
from sklearn.linear_model import LogisticRegression
from xgboost import XGBClassifier

# -------------------------
# 0) Helpers & assumptions
# -------------------------
# YÊU CẦU df có cột:
# 'date' (datetime-like), 'ticker', 'sector' (optional), 'vol_bucket' (optional),
# 'future_ret' (lợi nhuận 1m tương lai), 'cost' (phí/slippage per-trade),
# label binary ở 'label' (profit_1m>th ? 1:0).
# Feature cols bạn truyền vào qua feature_cols.

# Trading calendar & event windows
def make_trading_calendar(dates: pd.Series) -> np.ndarray:
    d = pd.to_datetime(dates).sort_values().unique()
    return d

def add_event_window(df: pd.DataFrame, time_col: str, H_days: int) -> pd.DataFrame:
    cal = make_trading_calendar(df[time_col])
    pos = pd.Series(np.arange(len(cal)), index=cal)
    idx = pos[pd.to_datetime(df[time_col]).values].values
    end_idx = np.minimum(idx + H_days, len(cal) - 1)
    df = df.copy()
    df['start'] = pd.to_datetime(df[time_col]).values
    df['end']   = cal[end_idx]
    return df

# Purge/Embargo: keep train samples that DO NOT overlap [val_start - E, val_end + E]
def purge_with_embargo(train_idx: np.ndarray, val_idx: np.ndarray,
                       start_s: pd.Series, end_s: pd.Series,
                       embargo_days: int) -> np.ndarray:
    vstart, vend = start_s.iloc[val_idx].min(), end_s.iloc[val_idx].max()
    e_start = vstart - pd.Timedelta(days=embargo_days)
    e_end   = vend   + pd.Timedelta(days=embargo_days)
    keep = []
    for i in train_idx:
        s, e = start_s.iat[i], end_s.iat[i]
        if (e < e_start) or (s > e_end):
            keep.append(i)
    return np.array(keep, dtype=int)

# Group theo tháng để làm folds thời gian
def month_group(dts: pd.Series) -> np.ndarray:
    return pd.to_datetime(dts).dt.to_period('M').astype(str).values

# -------------------------
# 1) Inner OOF p0 (time-aware)
# -------------------------
def inner_time_oof_p0(X: np.ndarray, y: np.ndarray, months: np.ndarray,
                      model_params: Dict[str, Any], n_splits: int = 5,
                      random_state: int = 2025) -> np.ndarray:
    unique_months = pd.Index(months).unique().tolist()
    # Chia thành n_splits khối thời gian liên tiếp
    splits = np.array_split(unique_months, n_splits)
    p0 = np.zeros(len(y), dtype=float)
    for k in range(n_splits):
        val_months = set(splits[k])
        val_idx = np.where(np.isin(months, list(val_months)))[0]
        train_idx = np.where(~np.isin(months, list(val_months)))[0]
        if len(val_idx) == 0 or len(train_idx) == 0:
            continue
        mdl = XGBClassifier(**model_params)
        mdl.fit(X[train_idx], y[train_idx])
        p0[val_idx] = mdl.predict_proba(X[val_idx])[:, 1]
    # Nếu tháng nào không được validate (rất hiếm), fill bằng mean
    mask_zero = (p0 == 0)
    if mask_zero.any():
        p0[mask_zero] = y.mean()
    return p0

# -------------------------
# 2) Sample Weights
# -------------------------
def time_decay_weights(dates: pd.Series, ref_date: Optional[pd.Timestamp] = None,
                       half_life_days: int = 126) -> np.ndarray:
    """
    Compute time-decay weights based on recency.
    Args:
        dates (pd.Series): Series of dates.
        ref_date (Optional[pd.Timestamp]): Reference date for decay.
        half_life_days (int): Half-life in days for decay.
    Returns:
        np.ndarray: Time-decay weights.

    Cách chọn bài bản
    Grid OOF: HL ∈ {63, 126, 189, 252}, chấm EV@θ, Sharpe quasi-live trên OOF; chọn mean − λ·std tốt nhất.
    """
    dates = pd.to_datetime(dates)
    ref = pd.to_datetime(ref_date) if ref_date is not None else dates.max()
    age = (ref - dates).dt.days.clip(lower=0)
    lam = np.log(2.0) / half_life_days
    return np.exp(-lam * age)

def uniqueness_weights(starts: pd.Series, ends: pd.Series) -> np.ndarray:
    """
        Compute uniqueness weights based on overlapping events.
        Args:
            starts (pd.Series): Start dates of events.
            ends (pd.Series): End dates of events.
        Returns:
            np.ndarray: Uniqueness weights.
    """
    smin, emax = starts.min().normalize(), ends.max().normalize()
    timeline = pd.date_range(smin, emax, freq='D')
    diff = pd.Series(0, index=timeline)
    for s, e in zip(starts, ends):
        diff.loc[s.normalize()] += 1
        endp1 = (e + pd.Timedelta(days=1)).normalize()
        if endp1 in diff.index:
            diff.loc[endp1] -= 1
    conc = diff.cumsum()
    w = []
    for s, e in zip(starts, ends):
        rng = conc.loc[s.normalize():e.normalize()]
        w.append((1.0 / rng).mean())
    w = np.array(w, dtype=float)
    return w / w.mean()

def uniqueness_weights_tradingbars(start_dates, end_dates, all_trading_dates):
    # start_dates/end_dates: pd.Series datetime cho từng sample (start, end=t+H theo TRADING DAY)
    # all_trading_dates: np.array các ngày giao dịch đã sort & unique

    # map date -> index (bar)
    pos = pd.Series(np.arange(len(all_trading_dates)), index=pd.to_datetime(all_trading_dates))

    s_idx = pos[pd.to_datetime(start_dates).values].values
    e_idx = pos[pd.to_datetime(end_dates).values].values

    T = len(all_trading_dates)
    diff = np.zeros(T + 1, dtype=float)
    np.add.at(diff, s_idx, 1.0)
    np.add.at(diff, e_idx + 1, -1.0)

    conc = np.cumsum(diff[:-1])                 # concurrency theo TRADING BAR
    inv  = 1.0 / np.maximum(conc, 1.0)
    pre  = np.concatenate([[0.0], np.cumsum(inv)])

    # mean(1/conc) trên cửa sổ của từng sample
    w = (pre[e_idx + 1] - pre[s_idx]) / (e_idx - s_idx + 1)
    w = w / w.mean()

    # nếu gần như không phân biệt được ⇒ tắt luôn cho đỡ ồn
    if (w.std() / (w.mean() + 1e-12)) < 0.02:
        w = np.ones_like(w)
    return w

def cost_aware_weights(future_ret: np.ndarray, cost: np.ndarray, eps: float = 1e-6) -> np.ndarray:
    """
       Compute cost-aware weights based on future returns and costs.
       Args:
           future_ret (np.ndarray): Array of future returns.
           cost (np.ndarray): Array of costs.
           eps (float): Small constant to avoid division by zero.
       Returns:
           np.ndarray: Cost-aware weights.
       """
    w = np.maximum(future_ret - cost, 0.0) + eps
    return w / w.mean()

def focal_like_weights(y: np.ndarray, p0: np.ndarray, alpha: Optional[float] = None,
                       gamma: float = 1.5) -> np.ndarray:
    """
        Compute focal-like weights for imbalanced classification.
        Args:
            y (np.ndarray): Binary target array.
            p0 (np.ndarray): Initial predictions.
            alpha (Optional[float]): Class imbalance parameter.
            gamma (float): Focusing parameter.
        Returns:
            np.ndarray: Focal-like weights.
    """
    y = y.astype(int)
    p = np.clip(p0, 1e-6, 1-1e-6)
    pos_rate = y.mean() if alpha is None else alpha
    w = np.where(y==1, pos_rate*(1-p)**gamma, (1-pos_rate)*p**gamma)
    return w / w.mean()

# -------------------------
# 3) 2-stage trainer (no calibration here)
# -------------------------
@dataclass
class TwoStageParams:
    stage1: Dict[str, Any]
    stage2: Dict[str, Any]
    gamma: float = 1.5
    alpha: Optional[float] = None  # focal alpha; None = auto by prevalence
    half_life_days: int = 126

def train_two_stage(X_tr: np.ndarray, y_tr: np.ndarray,
                    dates_tr: pd.Series, start_tr: pd.Series, end_tr: pd.Series,
                    future_ret_tr: np.ndarray, cost_tr: np.ndarray,
                    months_tr: np.ndarray,
                    params: TwoStageParams) -> Tuple[XGBClassifier, np.ndarray]:
    # Stage-1 (nhẹ) để tính p0
    mdl1 = XGBClassifier(**params.stage1)
    mdl1.fit(X_tr, y_tr)
    p0_in = mdl1.predict_proba(X_tr)[:, 1]  # có thể thay bằng inner OOF nếu muốn chặt hơn

    # Focal + time + uniq + cost weights
    # Bản “lite” (nhanh, 80% hiệu quả): w_all = w_focal × w_time
    # Bản “trading-first” (khuyến nghị): w_all = w_focal × w_time × w_cost
    # Bản “event-heavy”: thêm w_uniq khi bạn chuyển sang triple-barrier/đa-horizon.
    # Chạy 4 biến thể trên OOF:
    # Base (no weight)
    # focal
    # focal × time
    # focal × time × cost
    # (+ uniq nếu bạn nghi có lợi)
    # So sánh EV@θ* (tuned), Sharpe quasi-live, Lift@10%, ICIR.
    # Nếu uniq không kéo EV/Sharpe rõ rệt ⇒ loại.

    #
    w_time = time_decay_weights(dates_tr, half_life_days=params.half_life_days)
    w_uniq = uniqueness_weights(start_tr, end_tr)
    w_cost = cost_aware_weights(future_ret_tr, cost_tr)
    w_foc  = focal_like_weights(y_tr, p0_in, alpha=params.alpha, gamma=params.gamma)
    w_all  = w_time * w_uniq * w_cost * w_foc
    w_all  = w_all / w_all.mean()

    # Stage-2 (final)
    mdl2 = XGBClassifier(**params.stage2)
    mdl2.fit(X_tr, y_tr, sample_weight=w_all)
    return mdl2, w_all

# -------------------------
# 4) Metrics (trading-first)
# -------------------------
def ic_by_day(pred: np.ndarray, future_ret: np.ndarray, dates: pd.Series) -> Tuple[float, float]:
    # Spearman IC theo ngày rồi lấy mean & std
    df = pd.DataFrame({'pred': pred, 'ret': future_ret, 'date': pd.to_datetime(dates)})
    out = []
    for d, g in df.groupby('date'):
        if g['pred'].nunique() < 2 or g['ret'].nunique() < 2:
            continue
        out.append(spearmanr(g['pred'], g['ret']).correlation)
    if len(out)==0:
        return np.nan, np.nan
    arr = np.array(out, dtype=float)
    return float(arr.mean()), float(arr.std())

def lift_at_k(pred: np.ndarray, future_ret: np.ndarray, k_pct: float = 0.1) -> float:
    n = len(pred)
    k = max(1, int(n * k_pct))
    idx = np.argsort(pred)[-k:]
    return float(future_ret[idx].mean() - future_ret.mean())

def ev_at_threshold(pred: np.ndarray, future_ret: np.ndarray, cost: np.ndarray,
                    theta: float) -> float:
    sel = pred >= theta
    if sel.sum()==0:
        return np.nan
    return float((future_ret[sel] - cost[sel]).mean())

def tune_theta_ev(pred: np.ndarray, future_ret: np.ndarray, cost: np.ndarray,
                  grid: np.ndarray = np.linspace(0.5, 0.9, 9)) -> Tuple[float, float]:
    best_th, best_ev = None, -1e18
    for th in grid:
        ev = ev_at_threshold(pred, future_ret, cost, th)
        if np.isnan(ev): continue
        if ev > best_ev:
            best_ev, best_th = ev, th
    return best_th, best_ev

def sharpe_quasi_live(pred: np.ndarray, future_ret: np.ndarray, cost: np.ndarray,
                      dates: pd.Series, theta: float) -> float:
    # Giả sử mỗi ngày equal-weight long các mã có pred>=θ
    df = pd.DataFrame({'pred': pred, 'ret': future_ret, 'cost': cost, 'date': pd.to_datetime(dates)})
    pnl_daily = []
    for d, g in df.groupby('date'):
        sel = g['pred'] >= theta
        if sel.sum()==0:
            pnl_daily.append(0.0)
        else:
            pnl_daily.append((g.loc[sel, 'ret'] - g.loc[sel, 'cost']).mean())
    pnl_daily = np.array(pnl_daily, dtype=float)
    mu, sd = pnl_daily.mean(), pnl_daily.std(ddof=1) + 1e-9
    return float(mu / sd)

# -------------------------
# 5) Walk-Forward Splits (monthly)
# -------------------------
def generate_wfv_folds(df: pd.DataFrame, time_col: str = 'date',
                       train_window_months: int = 36,
                       val_window_months: int = 1,
                       step_months: int = 1,
                       start_date: Optional[str] = None,
                       end_date: Optional[str] = None) -> List[Dict[str, Any]]:
    dts = pd.to_datetime(df[time_col])
    months = dts.dt.to_period('M')
    m_all = months.unique().sort_values()

    if start_date is not None:
        m_all = m_all[m_all >= pd.Period(start_date, 'M')]
    if end_date is not None:
        m_all = m_all[m_all <= pd.Period(end_date, 'M')]

    folds = []
    # Val block = [m_i, m_i+val_window-1], Train = previous train_window months (<= m_i - 1)
    for i in range(train_window_months, len(m_all) - val_window_months + 1, step_months):
        val_start_m = m_all[i]
        val_end_m   = m_all[i + val_window_months - 1]
        train_start_m = m_all[i - train_window_months]
        train_end_m   = m_all[i - 1]
        folds.append(dict(
            fold_id=len(folds),
            train_month_start=str(train_start_m),
            train_month_end=str(train_end_m),
            val_month_start=str(val_start_m),
            val_month_end=str(val_end_m)
        ))
    return folds

def apply_wfv_with_purge(df: pd.DataFrame, folds: List[Dict[str, Any]],
                         time_col: str, embargo_days: int,
                         H_days: int) -> List[Tuple[np.ndarray, np.ndarray, Dict[str, Any]]]:
    # Chuẩn bị event windows
    dfe = add_event_window(df, time_col, H_days)
    dts = pd.to_datetime(dfe[time_col])
    months = dts.dt.to_period('M').astype(str).values
    start_s, end_s = dfe['start'], dfe['end']

    all_sets = []
    for F in folds:
        tr_mask = (months >= F['train_month_start']) & (months <= F['train_month_end'])
        va_mask = (months >= F['val_month_start']) & (months <= F['val_month_end'])
        train_idx = np.where(tr_mask)[0]
        val_idx   = np.where(va_mask)[0]
        # Purge+Embargo theo event windows
        train_idx = purge_with_embargo(train_idx, val_idx, start_s, end_s, embargo_days)
        info = dict(F)
        info.update(train_samples=len(train_idx), val_samples=len(val_idx))
        all_sets.append((train_idx, val_idx, info))
    return all_sets

# -------------------------
# 6) Master evaluator
# -------------------------
@dataclass
class EvalConfig:
    label_col: str = 'label'
    date_col: str  = 'date'
    ret_col: str   = 'future_ret'
    cost_col: str  = 'cost'
    ticker_col: str = 'ticker'
    H_days: int = 21
    embargo_days: int = 10
    train_window_months: int = 36
    val_window_months: int = 1
    step_months: int = 1
    # 2-stage params
    stage1_params: Dict[str, Any] = None
    stage2_params: Dict[str, Any] = None
    focal_gamma: float = 1.5
    focal_alpha: Optional[float] = None
    half_life_days: int = 126
    # metrics
    theta_grid: np.ndarray = np.linspace(0.55, 0.85, 7)
    lift_k_pct: float = 0.1

def evaluate_walk_forward(df: pd.DataFrame, feature_cols: List[str], cfg: EvalConfig) -> Dict[str, Any]:
    df = df.copy()
    df[cfg.date_col] = pd.to_datetime(df[cfg.date_col])

    # Folds (monthly)
    folds = generate_wfv_folds(
        df, time_col=cfg.date_col,
        train_window_months=cfg.train_window_months,
        val_window_months=cfg.val_window_months,
        step_months=cfg.step_months
    )
    sets = apply_wfv_with_purge(df, folds, cfg.date_col, cfg.embargo_days, cfg.H_days)

    # Default model params
    stage1 = cfg.stage1_params or dict(
        n_estimators=300, max_depth=4, learning_rate=0.07, subsample=0.8,
        colsample_bytree=0.6, reg_lambda=2.0, objective='binary:logistic',
        eval_metric='logloss', tree_method='hist', random_state=2025
    )
    stage2 = cfg.stage2_params or dict(
        n_estimators=900, max_depth=5, learning_rate=0.05, subsample=0.8,
        colsample_bytree=0.7, reg_lambda=2.0, reg_alpha=0.5,
        objective='binary:logistic', eval_metric='logloss',
        tree_method='hist', random_state=2025
    )
    ts_params = TwoStageParams(stage1=stage1, stage2=stage2,
                               gamma=cfg.focal_gamma, alpha=cfg.focal_alpha,
                               half_life_days=cfg.half_life_days)

    # Collect OOF-like predictions on each val block
    oof_rows = []
    for (tr_idx, va_idx, info) in sets:
        if len(tr_idx) < 200 or len(va_idx) < 50:
            continue

        X_tr = df.iloc[tr_idx][feature_cols].values
        y_tr = df.iloc[tr_idx][cfg.label_col].values.astype(int)
        dates_tr = df.iloc[tr_idx][cfg.date_col]
        start_tr = df.iloc[tr_idx]['start'] if 'start' in df.columns else add_event_window(df.iloc[tr_idx], cfg.date_col, cfg.H_days)['start']
        end_tr   = df.iloc[tr_idx]['end']   if 'end'   in df.columns else add_event_window(df.iloc[tr_idx], cfg.date_col, cfg.H_days)['end']
        fut_tr = df.iloc[tr_idx][cfg.ret_col].values
        cost_tr= df.iloc[tr_idx][cfg.cost_col].values
        months_tr = month_group(dates_tr)

        # 2-stage train w/ weights
        mdl, w_all = train_two_stage(X_tr, y_tr, dates_tr, start_tr, end_tr, fut_tr, cost_tr, months_tr, ts_params)

        # Predict on validation
        va = df.iloc[va_idx]
        X_va = va[feature_cols].values
        p_va = mdl.predict_proba(X_va)[:, 1]  # p_raw

        # Lưu OOF rows
        tmp = pd.DataFrame({
            'date': va[cfg.date_col].values,
            'ticker': va.get('ticker', pd.Series(index=va.index, dtype=object)).values,
            'p_raw': p_va,
            'future_ret': va[cfg.ret_col].values,
            'cost': va[cfg.cost_col].values,
            'sector': va.get('sector', pd.Series(index=va.index, dtype=object)).values,
            'vol_bucket': va.get('vol_bucket', pd.Series(index=va.index, dtype=object)).values,
            'fold_id': info['fold_id']
        })
        oof_rows.append(tmp)

    if len(oof_rows)==0:
        return {'oof': pd.DataFrame(), 'metrics': {}, 'folds': folds}

    oof = pd.concat(oof_rows, ignore_index=True)
    # ---------------- Metrics tổng hợp ----------------
    y_all = (oof['future_ret'] > 0).astype(int).values  # nếu muốn so nhãn nhị phân, hoặc dùng df[label] nếu giữ lại
    p_all = oof['p_raw'].values
    dates_all = oof['date']
    ret_all = oof['future_ret'].values
    cost_all= oof['cost'].values

    # AUC-PR & ROC-AUC (toàn OOF)
    auc_pr = average_precision_score(y_all, p_all) if (y_all.sum()>0 and (1-y_all).sum()>0) else np.nan
    roc    = roc_auc_score(y_all, p_all) if (y_all.sum()>0 and (1-y_all).sum()>0) else np.nan

    # IC & ICIR theo ngày
    ic_mean, ic_std = ic_by_day(p_all, ret_all, dates_all)
    icir = ic_mean / (ic_std + 1e-9) if pd.notnull(ic_mean) else np.nan

    # Lift@k (cross-sectional)
    lift10 = lift_at_k(p_all, ret_all, k_pct=cfg.lift_k_pct)

    # Theta tối ưu theo EV (global)
    best_th, best_ev = tune_theta_ev(p_all, ret_all, cost_all, cfg.theta_grid)
    sh = sharpe_quasi_live(p_all, ret_all, cost_all, dates_all, theta=best_th) if best_th is not None else np.nan

    metrics = {
        "AUC_PR": float(auc_pr) if auc_pr == auc_pr else np.nan,
        "ROC_AUC": float(roc) if roc == roc else np.nan,
        "IC_mean": float(ic_mean) if ic_mean == ic_mean else np.nan,
        "ICIR": float(icir) if icir == icir else np.nan,
        f"Lift_at_{cfg.lift_k_pct * 100:.0f}pct": float(lift10),
        "Best_theta": float(best_th) if best_th is not None else np.nan,
        "EV_at_Best_theta": float(best_ev) if best_th is not None else np.nan,
        "Sharpe_quasi_live": float(sh) if sh == sh else np.nan,
        "Num_samples": int(len(oof))
    }

    return {'oof': oof, 'metrics': metrics, 'folds': folds}






cfg = EvalConfig(
    label_col='label',
    date_col='date',
    ret_col='future_ret',
    cost_col='cost',
    H_days=21, embargo_days=10,
    train_window_months=36, val_window_months=1, step_months=1,
    theta_grid=np.linspace(0.55, 0.85, 7)
)

res = evaluate_walk_forward(df, feature_cols, cfg)
oof_df   = res['oof']        # chứa: date, ticker, p_raw, future_ret, cost, sector, vol_bucket, fold_id
metrics  = res['metrics']    # AUC-PR, ROC, IC, ICIR, Lift@k, EV@θ*, Sharpe_quasi_live
folds    = res['folds']      # thông tin train/val từng tháng










































# from typing import Iterator, Tuple, Optional
#
# class PurgedKFoldTimeSeriesSplit:
#     """
#     Purged K-Fold theo thời gian + Embargo.
#     - Chia theo mốc thời gian (Datetime), không shuffle.
#     - Purge theo t1: loại train windows mà [t0_i, t1_i] giao với test window.
#     - Embargo: loại thêm train sau test_end một đoạn e (Timedelta hoặc bars).
#     """
#     def __init__(
#         self,
#         n_splits: int = 5,
#         test_size: Optional[int] = None,         # số bars trong test mỗi fold (nếu dùng bars)
#         test_period: Optional[pd.DateOffset] = None,  # hoặc khoảng thời gian (ví dụ pd.DateOffset(months=3))
#         embargo: Optional[int | pd.Timedelta | pd.DateOffset] = None,  # bars hoặc thời gian
#         t1: Optional[pd.Series] = None,          # Series datetime kết thúc nhãn, index khớp df
#         rolling_train: bool = False,
#         min_train_size: int = 252               # dùng khi rolling_train=True: số bars train tối thiểu
#     ):
#         assert (test_size is not None) ^ (test_period is not None), "Set test_size (bars) hoặc test_period (DateOffset)!"
#         self.n_splits = n_splits
#         self.test_size = test_size
#         self.test_period = test_period
#         self.embargo = embargo
#         self.t1 = t1
#         self.rolling_train = rolling_train
#         self.min_train_size = min_train_size
#
#     def split(self, df: pd.DataFrame) -> Iterator[Tuple[np.ndarray, np.ndarray]]:
#         ti = get_time_index(df)
#         n = len(ti)
#         # Xây test windows theo thời gian
#         # Tạo các ranh giới fold đều nhau theo thời hoặc theo bars
#         # 1) Lấy mốc thời gian duy nhất (nếu multi-asset, nhiều symbol cùng timestamp -> OK)
#         uniq_time = pd.Index(ti.unique()).sort_values()
#
#         # 2) Chia test windows
#         test_windows = []
#         if self.test_size is not None:
#             # theo bars
#             step = len(uniq_time) // self.n_splits
#             # bảo đảm mỗi fold có test_size bars
#             starts = [i*step for i in range(self.n_splits)]
#             for s in starts:
#                 te_start_pos = s
#                 te_end_pos = min(s + self.test_size, len(uniq_time) - 1)
#                 test_windows.append((uniq_time[te_start_pos], uniq_time[te_end_pos]))
#         else:
#             # theo thời gian
#             total_span = uniq_time[-1] - uniq_time[0]
#             # ước lượng độ dài test block
#             # tìm ranh bằng cách "đi" theo test_period
#             cur = uniq_time[0]
#             bounds = []
#             while cur < uniq_time[-1]:
#                 nxt = cur + self.test_period
#                 if nxt > uniq_time[-1]:
#                     nxt = uniq_time[-1]
#                 bounds.append((cur, nxt))
#                 cur = nxt
#             # lấy n_splits block cuối (hoặc đều nhau)
#             if len(bounds) < self.n_splits:
#                 raise ValueError("Quá ít block thời gian so với n_splits.")
#             # chọn đều nhau trong bounds
#             idxs = np.linspace(0, len(bounds)-1, self.n_splits, dtype=int)
#             test_windows = [bounds[i] for i in idxs]
#
#         # 3) Tính t1 nếu cần
#         t1 = self.t1 if self.t1 is not None else ensure_t1(df, horizon_bars=None if 't1' in df else 1)
#         # Nếu t1 thiếu giá trị cuối -> fill bằng chính thời điểm đó (không gây purge thừa)
#         t0 = ti  # thời bắt đầu quan sát là index thời điểm đó
#         t1_filled = pd.Series(t1, index=df.index)
#         # ép thành DatetimeIndex
#         t1_filled = pd.to_datetime(t1_filled)
#
#         # 4) Yield các cặp (train_idx, test_idx)
#         for (te_start, te_end) in test_windows:
#             # test mask theo thời
#             te_mask = (ti >= te_start) & (ti <= te_end)
#             test_idx = np.flatnonzero(te_mask)
#
#             # purge train: loại bất kỳ hàng nào có [t0_i, t1_i] giao test window
#             # Điều kiện giao: (t0_i <= te_end) & (t1_i >= te_start)
#             overlap = (t0 <= te_end) & (t1_filled >= te_start)
#             train_mask = ~overlap
#
#             # embargo: loại thêm sau te_end
#             if self.embargo is not None:
#                 if isinstance(self.embargo, int):
#                     # bars
#                     # tìm vị trí cuối test theo uniq_time
#                     te_end_pos = uniq_time.get_indexer([te_end], method='pad')[0]
#                     embargo_end_pos = min(te_end_pos + self.embargo, len(uniq_time)-1)
#                     embargo_end_time = uniq_time[embargo_end_pos]
#                 else:
#                     embargo_end_time = te_end + self.embargo
#                 emb_mask = (ti > te_end) & (ti <= embargo_end_time)
#                 train_mask &= ~emb_mask
#
#             # rolling/exanding train range
#             if self.rolling_train:
#                 # chỉ giữ train trước te_start và lấy N bars gần nhất
#                 prior_mask = (ti < te_start) & train_mask
#                 prior_idx = np.flatnonzero(prior_mask)
#                 if len(prior_idx) > self.min_train_size:
#                     prior_idx = prior_idx[-self.min_train_size:]
#                 train_idx = prior_idx
#             else:
#                 # expanding: all before te_start sau khi purge + embargo
#                 train_idx = np.flatnonzero((ti < te_start) & train_mask)
#
#             # đảm bảo không rỗng
#             if len(train_idx) == 0 or len(test_idx) == 0:
#                 continue
#             yield train_idx, test_idx
#
#
#
# class WalkForwardSplit:
#     """
#     Chia theo block thời gian liên tiếp:
#     - Expanding: train = [start .. te_start - 1], test = [te_start .. te_end]
#     - Rolling:   train = cửa sổ cố định trước test
#     - gap: số bars bỏ qua giữa train và test để tránh leak khi horizon cố định
#     """
#     def __init__(
#         self,
#         n_splits: int = 8,
#         test_size: int = 60,             # bars
#         rolling_train: bool = False,
#         train_window: int = 252,         # bars (khi rolling)
#         gap: int = 0                     # bars
#     ):
#         self.n_splits = n_splits
#         self.test_size = test_size
#         self.rolling_train = rolling_train
#         self.train_window = train_window
#         self.gap = gap
#
#     def split(self, df: pd.DataFrame):
#         ti = get_time_index(df)
#         uniq_time = pd.Index(ti.unique()).sort_values()
#         step = (len(uniq_time) - self.test_size) // self.n_splits
#         for k in range(self.n_splits):
#             te_start_pos = k * step
#             te_end_pos   = min(te_start_pos + self.test_size - 1, len(uniq_time)-1)
#             te_start, te_end = uniq_time[te_start_pos], uniq_time[te_end_pos]
#
#             # gap giữa train và test
#             gap_end_pos = max(0, te_start_pos - self.gap - 1)
#             gap_end_time = uniq_time[gap_end_pos] if self.gap > 0 else uniq_time[te_start_pos-1] if te_start_pos>0 else uniq_time[0]
#
#             if self.rolling_train:
#                 tr_end_pos = max(0, te_start_pos - self.gap - 1)
#                 tr_start_pos = max(0, tr_end_pos - self.train_window + 1)
#                 tr_start, tr_end = uniq_time[tr_start_pos], uniq_time[tr_end_pos]
#                 train_mask = (ti >= tr_start) & (ti <= tr_end)
#             else:
#                 # expanding
#                 train_mask = (ti < te_start)
#                 if self.gap > 0:
#                     train_mask = train_mask & (ti <= gap_end_time)
#
#             test_mask = (ti >= te_start) & (ti <= te_end)
#             train_idx = np.flatnonzero(train_mask)
#             test_idx  = np.flatnonzero(test_mask)
#             if len(train_idx) == 0 or len(test_idx) == 0:
#                 continue
#             yield train_idx, test_idx
#
#
# from xgboost import XGBClassifier
# from sklearn.metrics import roc_auc_score
# import numpy as np
#
# def cv_oos_predictions(df, features, splitter, sample_weight=None, xgb_params=None, early_stopping_rounds=100):
#     """
#     Trả về dự báo OOS cho toàn bộ điểm test của các fold (không rò rỉ).
#     """
#     if xgb_params is None:
#         xgb_params = dict(
#             n_estimators=2000,
#             learning_rate=0.03,
#             max_depth=6,
#             subsample=0.8,
#             colsample_bytree=0.8,
#             tree_method="hist",
#             objective="binary:logistic",
#             eval_metric="auc"
#         )
#     proba = pd.Series(index=df.index, dtype=float)
#     y_true = pd.Series(index=df.index, dtype=float)
#     ti = get_time_index(df)
#
#     for tr_idx, te_idx in splitter.split(df):
#         X_tr = df.iloc[tr_idx][features].values
#         y_tr = df.iloc[tr_idx]["y"].values
#         X_te = df.iloc[te_idx][features].values
#         y_te = df.iloc[te_idx]["y"].values
#
#         sw_tr = None
#         if sample_weight is not None:
#             sw_tr = sample_weight.iloc[tr_idx].values
#
#         # early stopping trên một slice cuối của train (hoặc dùng CV nội bộ nhỏ)
#         # Ở đây mình tách 10% cuối train làm valid (vẫn theo thời gian)
#         n_tr = len(tr_idx)
#         val_cut = int(n_tr * 0.9)
#         X_tr_fit, y_tr_fit = X_tr[:val_cut], y_tr[:val_cut]
#         X_val, y_val = X_tr[val_cut:], y_tr[val_cut:]
#         sw_fit = sw_tr[:val_cut] if sw_tr is not None else None
#         sw_val = sw_tr[val_cut:] if sw_tr is not None else None
#
#         model = XGBClassifier(**xgb_params)
#         model.fit(
#             X_tr_fit, y_tr_fit,
#             eval_set=[(X_val, y_val)],
#             sample_weight=sw_fit,
#             eval_sample_weight=[sw_val] if sw_val is not None else None,
#             verbose=False,
#             early_stopping_rounds=early_stopping_rounds
#         )
#         proba.iloc[te_idx] = model.predict_proba(X_te)[:, 1]
#         y_true.iloc[te_idx] = y_te
#
#     # AUC OOS tổng
#     valid = proba.notna()
#     auc = roc_auc_score(y_true[valid], proba[valid])
#     return proba, y_true, auc
#
#
#
#
# # # giả sử df có cột 't1' (Datetime) và 'y'
# # t1_series = pd.to_datetime(df['t1'])
# # splitter = PurgedKFoldTimeSeriesSplit(
# #     n_splits=8,
# #     test_period=pd.DateOffset(months=4),  # mỗi fold test 4 tháng
# #     embargo=pd.DateOffset(days=10),       # hoặc embargo = 10 (bars)
# #     t1=t1_series,
# #     rolling_train=False                   # True nếu muốn rolling window
# # )
# # proba, y_true, auc = cv_oos_predictions(df, features, splitter)
# # print("OOS AUC:", auc)
#
#
# # H = 5  # ví dụ label nhìn 5 phiên
# # splitter = WalkForwardSplit(
# #     n_splits=10,
# #     test_size=60,        # 60 bars ~ 3 tháng nếu ~20 bars/tháng
# #     rolling_train=True,  # dùng cửa sổ gần
# #     train_window=252,    # ~1 năm
# #     gap=H                # chống leak theo horizon
# # )
# # proba, y_true, auc = cv_oos_predictions(df, features, splitter)
# # print("OOS AUC:", auc)

