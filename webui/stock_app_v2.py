import collections
import gc
import json
import multiprocessing
import os
import re
import subprocess
import time
import warnings
from datetime import datetime, timezone
from io import BytesIO

import numpy as np
import pandas as pd
import plotly.graph_objects as go
import plotly.subplots as sp
import streamlit as st
import streamlit_authenticator as stauth
import yaml
from pathos.multiprocessing import ProcessingPool as Pool
from stqdm import stqdm
from yaml.loader import SafeLoader
from webui.plot_service import PlotService
from webui.utils import AllEvaluation, PreProcess, Simulation_all, memory, WeightEvaluation, Simulation_weight, q, \
    ShortEvaluation, block_by_index
from typing import Dict, List, Tuple, Any
from st_aggrid import AgGrid, GridOptionsBuilder, GridUpdateMode

os.environ["SERVICE"] = "WEBUI"
from core_utils.log import logger
from core_utils.dictionary import DICTIONARY

warnings.simplefilter(action='ignore')

from core_utils.constant import SIMULATE_FULL_CASH, SIMULATE_FULL_CASH_NOT_FIX, SIMULATE_ALLOCATE, FPATH, NUM_PROCESS


def load_data(fname, dict_filter=None, fpath=FPATH):
    start, end = parse_time(dict_filter)
    df = pd.read_csv(fpath + fname.replace('.csv', '').replace('*', '') + '.csv', dtype={'time': str, 'ticker': str}) \
        .query(f'time>="{start}" and time<="{end}"').reset_index(drop=True)
    df['ymd'] = pd.to_datetime(df['time'])
    return df


def edit_ticker_and_display(df, dict_filter=None):
    proflie_indicators = extract_indicators(dict_filter)
    df = df[proflie_indicators].sort_values(by=['time'], ascending=False)

    gb = GridOptionsBuilder.from_dataframe(df)
    gb.configure_pagination(paginationAutoPageSize=False, paginationPageSize=50)
    gb.configure_default_column(
        resizable=False,
        suppressSizeToFit=True,
        filter=True,
        groupable=False,
        sorteable=False
    )
    grid_options = gb.build()

    return df, grid_options


def apply_filter(df, filter):
    idx = df.query(filter).index
    return df.index.isin(idx).astype(int)


def preprocess_dictFilter(strFilter):
    dictFilter = json.loads(strFilter)
    # print("input",dictFilter)
    list_key = list(dictFilter.keys())
    for f in list_key:
        if f.startswith('_'):
            dictFilter[f[1:]] = dictFilter[f]
    list_key = dictFilter.keys()
    for f in list_key:
        for k in list_key:
            dictFilter[f] = dictFilter[f].replace("{" + k + "}", "(" + dictFilter[k] + ")")
    # print("output",dictFilter)
    return dictFilter


def preprocess_dictWeightFilter(strFilter):
    dictW = json.loads(strFilter)
    list_key = list(dictW.keys())

    dictWeight = {}
    for key in list_key:
        if key.startswith('_') or key.startswith('~'):
            new_key = key[1:]
            dictWeight[new_key] = dictW[key]
    return dictWeight


def extract_indicators(dictionary):
    values_list = []
    for key, value in dictionary.items():
        if key.startswith('_') or key.startswith('~'):
            # Loại bỏ chuỗi trong dấu nháy đơn và nháy kép
            cleaned_value = re.sub(r'(["\'])(?:(?=(\\?))\2.)*?\1', '', value)

            # Tìm các biến hợp lệ sau khi đã loại bỏ literal strings
            variables = re.findall(r'\b[A-Za-z_]\w*\b', cleaned_value)
            variables = [
                var for var in variables
                if not var.isdigit() and var.lower() not in (
                    'and', 'or', 'not', 'if', 'else', 'true', 'false', 'null', 'none',
                    'in', 'is', 'ge', 'le', 'gt', 'lt', 'eq', 'ne', 'abs', 'acos', 'acosh',
                    'asin', 'asinh', 'atan', 'atanh', 'ceil', 'cos', 'cosh', 'exp'
                )
            ]
            values_list.extend(variables)

    values_list = sorted(set(values_list))
    values_list = [ind for ind in values_list if ind not in ["time", "ticker"]]

    result = ["time", "ticker"]
    result.extend(values_list)
    return result


def check_parentheses_balance(expression):
    stack = []
    for char in expression:
        if char == '(':
            stack.append(char)
        elif char == ')':
            if not stack:
                raise ValueError('Missing or extra "(,)"')
            stack.pop()
    if stack:
        raise ValueError('Missing or extra "(,)"')


def check_logical_operators(expression):
    tokens = re.split(r'([&|()]|[<>!=]+)', re.sub(r'\s+', '', expression))

    logical_operators = {'&', '|', '&&', '||'}
    comparison_operators = {'==', '!=', '<', '>', '<=', '>='}
    previous_token = ''

    for token in tokens:
        token = token.strip()
        if token == '':
            continue
        if token in logical_operators:
            if previous_token in logical_operators or previous_token == '' or previous_token == '(':
                raise ValueError("Wrong logical operator")
        if token == '(' and previous_token == ')':
            raise ValueError("Miss logical operator")

        if token in comparison_operators:
            if previous_token in comparison_operators or previous_token == '' or previous_token == '(':
                raise ValueError("Wrong comparison operator")

        previous_token = token


def check_comparison_operators(expression):
    tokens = re.split(r'([!=<>]+)', expression)

    for i in range(1, len(tokens), 2):
        if tokens[i] not in {'==', '!=', '<', '>', '<=', '>='}:
            raise ValueError("Wrong comparison operator")


def save_state(state, value):
    if not isinstance(state, list):
        state = list(state)

    if not isinstance(value, list):
        value = list(value)

    for key, value in zip(state, value):
        st.session_state[key] = value


def parse_time(dict_filter):
    dates = []
    date_pattern = r"\d{4}-\d{2}-\d{2}"

    for key, value in dict_filter.items():
        dates.extend(re.findall(date_pattern, value))

    date_objects = [datetime.strptime(date, "%Y-%m-%d") for date in dates]
    min_date = min(date_objects)
    min_date_str = min_date.strftime("%Y-%m-%d")

    max_date = max(date_objects)
    max_date_str = max_date.strftime("%Y-%m-%d")

    return min_date_str, max_date_str


def process_ticker(args) -> Any:
    ticker, queue, eval_func = args
    try:
        df = pd.read_csv(f'{FPATH}/{ticker}.csv')
        result = eval_func(ticker, df)
        queue.put("done")
        return result
    except Exception as error:
        queue.put(f"Error: {ticker}: {error}")
        logger.exception(f"Error: {ticker}: {error}")


def parallel_process(eval_func: callable) -> List[Any]:
    list_processed_ticker = [f.replace('.csv', '') for f in os.listdir(FPATH) if f.endswith('.csv')]

    num_procs = NUM_PROCESS
    error_list = []
    with stqdm(total=len(list_processed_ticker)) as pbar:
        lres = []
        with Pool(num_procs) as p:
            queue = multiprocessing.Manager().Queue()
            # queue = q()
            args = [(ticker, queue, eval_func) for ticker in list_processed_ticker]

            output = p.amap(process_ticker, args)

            while not output.ready():
                if not queue.empty():
                    value = queue.get()
                    pbar.update(1)
                    if "Error" in str(value):
                        error_list.append(value)
                        # st.error(value)
        lres.extend(output.get())
    return lres


@memory.cache
def evaluate_for_weight(d_filters: Dict, d_weights: Dict, cutloss: float, lookback: int, k_exp: float, thres_buy: float,
                        thres_sell: float) -> List[Tuple]:
    def eval_func(ticker: str, pdxx: pd.DataFrame) -> Tuple:
        eval_ticker = WeightEvaluation(ticker, pdxx, d_filters, cutloss=cutloss, weight=d_weights,
                                       lookback=lookback, k_exp=k_exp, threshold_buy=thres_buy,
                                       threshold_sell=thres_sell)
        res_1 = eval_ticker.get_eval_weight_hit()
        res_2 = eval_ticker.get_df_weight()
        return res_1, res_2

    return parallel_process(eval_func)


@memory.cache
def evaluate_for_all(d_filters: Dict, cutloss: float, **kwargs) -> List[Tuple]:
    df_market=block_by_index()

    def eval_func(ticker: str, pdxx: pd.DataFrame) -> Tuple:
        eval_ticker = AllEvaluation(ticker, pdxx, d_filters, cutloss=cutloss, df_market=df_market)

        res_h = eval_ticker.get_hit()
        res_d = eval_ticker.get_deal(**kwargs)
        res_b = eval_ticker.append_full_indicators(res_d)
        return res_h, res_b, res_d

    return parallel_process(eval_func)


@memory.cache
def evaluate_for_shortsell(d_filters: Dict, cutloss: float) -> List[Tuple]:
    def eval_func(ticker: str, pdxx: pd.DataFrame) -> Tuple:
        eval_ticker = ShortEvaluation(ticker, pdxx, d_filters, cutloss=cutloss)
        res_s = eval_ticker.get_shortsell()
        res_f = eval_ticker.append_full_indicators(res_s)
        return res_s, res_f

    return parallel_process(eval_func)


@memory.cache
def eval_filter_by_weight(dictFilter, weights, lookback=5, k_exp=0, thres_buy=1, thres_sell=-1, CUTLOSS=0.15,
                          simulation_type=SIMULATE_ALLOCATE, simulative_slots=10, simulative_assets=1e8):
    lres = evaluate_for_weight(dictFilter, d_weights=weights, cutloss=CUTLOSS, lookback=lookback, k_exp=k_exp,
                               thres_buy=thres_buy, thres_sell=thres_sell)

    with st.spinner('⏳ Wait for calculating'):
        try:
            # df = pd.concat([res for res in lres if res is not None and not res.empty], axis=0). \
            #     sort_values('time', ascending=True).reset_index(drop=True)
            df = pd.concat([res[0] for res in lres if res is not None and not res[0].empty], axis=0). \
                sort_values('time', ascending=True).reset_index(drop=True)
            df_weight = pd.concat([res[1] for res in lres if res is not None and not res[1].empty], axis=0). \
                sort_values('time', ascending=True).reset_index(drop=True)
        except Exception as e:
            st.error(e)
            st.warning("Please review your filters and try again. \n At least 1 buy 1 sell pattern?")
            return [pd.DataFrame()] * 7

        df_process = PreProcess()
        df = df_process.weight_hits(df)

        df_weight["quarter"] = pd.to_datetime(df_weight['time']).dt.to_period('Q').astype(str)
        df_weight["quarter_fr"] = df_weight['time'].apply(lambda x: df_process.quarter_report(x))

        _df_q = df.groupby(['quarter'], as_index=False).agg({
            'p_win': 'count',
            'p_loss': 'count',
            'p_cutloss': 'count',
        })
        _df_q.rename(columns={'p_win': 'Win', 'p_loss': 'Loss', 'p_cutloss': 'Cutloss'}, inplace=True)
        _df_q_tail = _df_q.tail(9).iloc[:-1]
        ###
        _df_q_fr_s = df_process.group_by(df, ['ticker', 'quarter_fr', 'score'])
        _df_weight_q_fr_s = df_process.group_by(df_weight, ['ticker', 'quarter_fr', 'score'])

        ###
        start_date, end_date = parse_time(dictFilter)

        simulate_cols = ['ticker', 'time', 'Close', 'Price', 'buy_price', 'Volume', 'Volume_1M', 'Volume_1M_P50',
                         'score', 'sell_time', 'sell_filter', 'sell_score', 'sell_price', 'profit',
                         'holding_period', 'quarter', 'quarter_fr', 'Volume_sell', 'Low_sell', 'Open_sell', 'High_sell',
                         'Close_sell', 'Close_T1_sell']

        simulation = Simulation_weight(start_date=start_date, end_date=end_date, num_proc=NUM_PROCESS,
                                       initial_assets=simulative_assets, max_deals=simulative_slots)
        simulation_results = simulation.run_fast(df_deals=df[simulate_cols], iterate=30, s_type=simulation_type)

        # simulation logs
        detail_simulation_result = simulation.get_detail(s_type=simulation_type)
        pd_keep = detail_simulation_result['completed_deals']
        pd_skip = detail_simulation_result['skip_deals']
        simulative_log = detail_simulation_result['detail_log']

        si_cols = ['si_hit', 'si_total_time', 'si_time_in_market', 'si_cash_profit', 'si_profit', 'si_return',
                   'si_return_std', 'si_utilization', 'si_win_deal', 'si_win_quarter', 'si_ticker_diversity',
                   'si_quarter_ticker_diversity', 'si_unique_ticker', 'si_unique_quarter_ticker',
                   'si_peak_number_deals']
        si_result_cols = ['match_deals', 'total_time', 'time_in_market', 'cash_profit', 'profit', 'return',
                          'return_std', 'utilization', 'win_deal', 'win_quarter', 'set_ticker',
                          'set_quarter_ticker', 'unique_ticker', 'unique_quarter_ticker', 'peak_number_deals']

        si_result = {}
        for si_col, si_result_col in zip(si_cols, si_result_cols):
            si_result[si_col] = simulation_results['sum_weight'][si_result_col]

        # Distribute, statistical by hits
        bins = 10
        pd_distribute = df[
            ['score', 'p_win', 'p_loss', 'p_cutloss', 'p_sell', 'corr_s2p_on_ticker', 'P1W', 'P2W', 'P3W', 'P1M',
             'P2M', 'P3M', 'P6M', 'P1Y', 'P2Y']].copy()
        pd_distribute['score'] = pd_distribute['score'].astype(float)
        binx = sorted(list(set(np.percentile(pd_distribute['score'].dropna(), np.arange(0, 101, 100 / bins)))))
        pd_distribute['score'] = pd.cut(pd_distribute['score'], bins=binx)

        pd_distribute['count'] = 1
        pd_distribute['win_count'] = pd_distribute['p_win']
        pd_distribute['loss_count'] = pd_distribute['p_loss']
        pd_distribute['cutloss_count'] = pd_distribute['p_cutloss']

        pd_distribute = pd_distribute.groupby('score', as_index=False, observed=False).agg({
            'count': 'sum',
            "win_count": 'count',
            "loss_count": 'count',
            "cutloss_count": 'count',
            'p_sell': 'mean',
            'corr_s2p_on_ticker': 'mean',
            'P1W': 'mean',
            'P1M': 'mean',
            'P3M': 'mean',
            'P6M': 'mean',
            'P1Y': 'mean',
            'P2Y': 'mean',
        })
        pd_distribute['%win'] = pd_distribute['win_count'] / pd_distribute['count']
        pd_distribute['%loss'] = pd_distribute['loss_count'] / pd_distribute['count']
        pd_distribute['%cutloss'] = pd_distribute['cutloss_count'] / pd_distribute['count']
        pd_distribute = pd_distribute[
            ['score', 'count', '%win', '%loss', '%cutloss', 'p_sell', 'corr_s2p_on_ticker', 'P1W', 'P1M', 'P3M',
             'P6M', 'P1Y', 'P2Y']]

        # Distribute, statistical by hits are deduplicated by (ticker, quarter_fp, score)
        bins = 10
        pd_distribute_q = _df_q_fr_s[
            ['score', 'p_win', 'p_loss', 'p_cutloss', 'p_sell', 'corr_s2p_on_ticker', 'P1W', 'P2W', 'P3W', 'P1M',
             'P2M', 'P3M', 'P6M', 'P1Y', 'P2Y']].copy()
        pd_distribute_q['score'] = pd_distribute_q['score'].astype(float)
        binx = sorted(list(set(np.percentile(pd_distribute_q['score'].dropna(), np.arange(0, 101, 100 / bins)))))
        pd_distribute_q['score'] = pd.cut(pd_distribute_q['score'], bins=binx)

        pd_distribute_q['count'] = 1
        pd_distribute_q['win_count'] = pd_distribute_q['p_win']
        pd_distribute_q['loss_count'] = pd_distribute_q['p_loss']
        pd_distribute_q['cutloss_count'] = pd_distribute_q['p_cutloss']

        pd_distribute_q = pd_distribute_q.groupby('score', as_index=False, observed=False).agg({
            'count': 'sum',
            "win_count": 'count',
            "loss_count": 'count',
            "cutloss_count": 'count',
            'p_sell': 'mean',
            'corr_s2p_on_ticker': 'mean',
            'P1W': 'mean',
            'P1M': 'mean',
            'P3M': 'mean',
            'P6M': 'mean',
            'P1Y': 'mean',
            'P2Y': 'mean',
        })
        pd_distribute_q['%win'] = pd_distribute_q['win_count'] / pd_distribute_q['count']
        pd_distribute_q['%loss'] = pd_distribute_q['loss_count'] / pd_distribute_q['count']
        pd_distribute_q['%cutloss'] = pd_distribute_q['cutloss_count'] / pd_distribute_q['count']
        pd_distribute_q = pd_distribute_q[
            ['score', 'count', '%win', '%loss', '%cutloss', 'p_sell', 'corr_s2p_on_ticker', 'P1W', 'P1M', 'P3M',
             'P6M', 'P1Y', 'P2Y']]

        # Distribute, statistical by filter are deduplicated by (ticker, quarter_fp, score)
        bins = 10
        pd_distribute_score = _df_weight_q_fr_s[
            ['score', 'P1W', 'P2W', 'P3W', 'P1M', 'P2M', 'P3M', 'P6M', 'P1Y', 'P2Y']].copy()
        pd_distribute_score['score'] = pd_distribute_score['score'].astype(float)

        eee = 10e-8
        # dive data to 3 space
        ranges = [(-np.inf, -1 + eee), (-1 + eee, 1 - eee), (1 - eee, np.inf)]
        final_bins = []

        for r_min, r_max in ranges:
            mask = (pd_distribute_score['score'] > r_min) & (pd_distribute_score['score'] < r_max)
            subset = pd_distribute_score.loc[mask, 'score']

            if not subset.empty:
                bin_edges = sorted(list(set(np.percentile(subset.dropna(), np.linspace(0, 100, bins + 1)))))
                final_bins.extend(bin_edges)

        final_bins = sorted(list(set(final_bins)))
        pd_distribute_score['score'] = pd.cut(pd_distribute_score['score'], bins=final_bins)
        pd_distribute_score['count'] = 1

        aggs_f = {
            'count': [('', 'count')],
            'P1W': [('mean', 'mean'), ('std', lambda x: np.std(x, ddof=0))],
            'P2W': [('mean', 'mean'), ('std', lambda x: np.std(x, ddof=0))],
            'P3W': [('mean', 'mean'), ('std', lambda x: np.std(x, ddof=0))],
            'P1M': [('mean', 'mean'), ('std', lambda x: np.std(x, ddof=0))],
            'P2M': [('mean', 'mean'), ('std', lambda x: np.std(x, ddof=0))],
            'P3M': [('mean', 'mean'), ('std', lambda x: np.std(x, ddof=0))],
            'P6M': [('mean', 'mean'), ('std', lambda x: np.std(x, ddof=0))],
            'P1Y': [('mean', 'mean'), ('std', lambda x: np.std(x, ddof=0))],
            'P2Y': [('mean', 'mean'), ('std', lambda x: np.std(x, ddof=0))],
        }
        for per in ['1W', '2W', '3W', '1M', '2M', '3M', '6M', '1Y', '2Y']:
            pd_distribute_score[f'%win_{per}'] = pd_distribute_score[f'P{per}'].where(
                pd_distribute_score[f'P{per}'] > 0,
                np.nan)
            aggs_f[f'%win_{per}'] = [('', 'count')]

        pd_distribute_score = pd_distribute_score.groupby('score', as_index=False, observed=False).agg(aggs_f)
        pd_distribute_score.columns = ["_".join(col) if col[1] != '' else col[0] for col in pd_distribute_score.columns]

        for per in ['1W', '2W', '3W', '1M', '2M', '3M', '6M', '1Y', '2Y']:
            pd_distribute_score[f'%win_{per}'] = pd_distribute_score[f'%win_{per}'] / pd_distribute_score['count']

        # lp = [0, 10, 20, 50, 80, 90, 100]
        # pd.DataFrame({'p': lp, 'profit': np.percentile(pdx['sell_profit'], lp),
        #               'hold': np.percentile(pdx['holding_session'], lp)})
        #

        # Summary
        pd_summary = pd.DataFrame({
            'hit': df.shape[0],
            'si_return': si_result['si_return'],
            'si_return_std': si_result['si_return_std'],
            'profit_expected': df['p_sell'].mean(),
            '%win': df['p_win'].count() / df.shape[0] * 100,
            '%loss': df['p_loss'].count() / df.shape[0] * 100,
            '%cutloss': df['p_cutloss'].count() / df.shape[0] * 100,
            '%win_quarter': (_df_q[(_df_q['Win'] > (_df_q['Loss'] + _df_q['Cutloss']))].shape[0] / _df_q.shape[
                0]) * 100,
            '%winblock_8q': (_df_q_tail[(_df_q_tail['Win'] > (_df_q_tail['Loss'] + _df_q_tail['Cutloss']))].shape[0] /
                             _df_q_tail.shape[0]) * 100,
            'profit_win': df['p_win'].mean(),
            'profit_loss': df['p_loss'].mean(),
            'profit_cutloss': df['p_cutloss'].mean(),
            'holding_period': df['holding_period'].mean(),
            'corr_score_profit_on_ticker': df['corr_s2p_on_ticker'].mean(),
            'si_total_time': si_result['si_total_time'],
            'si_time_in_market': si_result['si_time_in_market'],
            'si_hit': si_result['si_hit'],
            'si_ticker_diversity': si_result['si_ticker_diversity'],
            'si_q_ticker_diversity': si_result['si_quarter_ticker_diversity'],
            'si_profit': si_result['si_profit'],
            'si_cash_profit': si_result['si_cash_profit'],
            'si_utilization': si_result['si_utilization'],
            'si_%win_deal': si_result['si_win_deal'],
            'si_%win_quarter': si_result['si_win_quarter'],
            'si_unique_q_tickers': si_result['si_unique_quarter_ticker'],
            'si_peak_number_deals': si_result['si_peak_number_deals']
        }, index=[0])

        now = (datetime.now(timezone.utc).strftime("%Y-%m-%d:%H:%M:%S"))
        logger.info(
            f"## {now} -- Sum P_performance: {(pd_summary['si_return'] * pd_summary['si_hit']).sum() / pd_summary['si_hit'].sum()}")

        return pd_summary, pd_keep, pd_skip, simulative_log, pd_distribute, pd_distribute_q, pd_distribute_score


@memory.cache
def eval_filter_all(dictFilter, CUTLOSS=0.1, simulative_type=SIMULATE_ALLOCATE, simulative_slots=10,
                    simulative_assets=1e8, combine_type='synthetic', combine_weight=None, combine_only=False,
                    combine_block=False, force_block_nums=None, enable_block_overheating=False):
    """ Return dataframe of profit
        pdp: dataframe of profit
        pdx: dataframe of historical hit
        pdy: dataframe of latest hit
    """

    def ranking_score_func(deal_df, sumary_df, rank_col, weight):
        if rank_col == 'technical_point':
            conditions = {
                "w_1": deal_df['D_RSI'] / deal_df['D_RSI_T1W'] > 1,
                "w_2": deal_df['Close'] / deal_df['VAP1W'] > 1,
                "w_3": deal_df['D_MFI'] / deal_df['D_MFI_T1W'] > 1,
                "w_4": deal_df['D_MACD'] / deal_df['D_MACD_T1W'] > 1,
                "w_5": (deal_df['Close'] / deal_df['LO_3M_T1'] > 0.97) & (deal_df['Close'] / deal_df['LO_3M_T1'] < 1.2),
                "w_6": deal_df['Volume'] / deal_df['Volume_1M'] > 1,
                "w_7": deal_df['D_RSI_Max1W'] / deal_df['D_RSI_Max3M'] > 1,
                "w_8": 1,
            }

            deal_df[rank_col] = sum(weight.get(k, 0) * v for k, v in conditions.items())

        if rank_col == 'weight_point':
            pass

        if rank_col == 'order_point':
            conditions = {
                "w_1": 'BKMA200',
                "w_2": 'TrendingGrowth',
                "w_3": 'TL3M',
                "w_4": 'BuySupport',
                "w_5": 'RSILow30',
                "w_6": 'UnderBV',
                "w_7": 'SuperGrowth',
                "w_8": 'SurpriseEarning',
                "w_9": 'Conservative',
                "w_10": 'BullDvg',
                "w_11": 'VolMax1Y',
                "w_12": 'T3P4',
            }
            filter_scores = pd.DataFrame({
                'filter': [v for k, v in conditions.items()],
                rank_col: [weight.get(k, 0) for k, v in conditions.items()]
            })

            deal_df = deal_df.merge(filter_scores, on='filter', how='left')

        if rank_col == 'synthetic':
            buy_filter = list(deal_df['filter'].unique())
            deal_df[buy_filter] = 0
            grouped = deal_df.groupby(["time", "ticker"])
            deal_df = grouped.apply(lambda group: group.assign(**{filter: 1 for filter in group['filter']}))
            conditions = {
                "w_1": deal_df['D_RSI'] / deal_df['D_RSI_T1W'] > 1,
                "w_2": deal_df['Close'] / deal_df['VAP1W'] > 1,
                "w_3": deal_df['D_MFI'] / deal_df['D_MFI_T1W'] > 1,
                "w_4": deal_df['D_MACD'] / deal_df['D_MACD_T1W'] > 1,
                "w_5": (deal_df['Close'] / deal_df['LO_3M_T1'] > 0.97) & (deal_df['Close'] / deal_df['LO_3M_T1'] < 1.2),
                "w_6": deal_df['Volume'] / deal_df['Volume_1M'] > 1,
                "w_7": deal_df['D_RSI_Max1W'] / deal_df['D_RSI_Max3M'] > 1,
                "w_8": deal_df['FSCORE'] / 9,
                "w_9": deal_df['BKMA200'] if 'BKMA200' in deal_df.columns else 0,
                "w_10": deal_df['BullDvg'] if 'BullDvg' in deal_df.columns else 0,
                "w_11": deal_df['BuySupport'] if 'BuySupport' in deal_df.columns else 0,
                "w_12": deal_df['Conservative'] if 'Conservative' in deal_df.columns else 0,
                "w_13": deal_df['RSILow30'] if 'RSILow30' in deal_df.columns else 0,
                "w_14": deal_df['SuperGrowth'] if 'SuperGrowth' in deal_df.columns else 0,
                "w_15": deal_df['SurpriseEarning'] if 'SurpriseEarning' in deal_df.columns else 0,
                "w_16": deal_df['TL3M'] if 'TL3M' in deal_df.columns else 0,
                "w_17": deal_df['TrendingGrowth'] if 'TrendingGrowth' in deal_df.columns else 0,
                "w_18": deal_df['UnderBV'] if 'UnderBV' in deal_df.columns else 0,
                "w_19": deal_df['VolMax1Y'] if 'VolMax1Y' in deal_df.columns else 0,
                "w_20": deal_df['T3P4'] if 'T3P4' in deal_df.columns else 0,
                "w_21": 1,
            }

            deal_df[rank_col] = sum(weight.get(k, 0) * v for k, v in conditions.items())

        if rank_col == 'ranking_point':
            sumary_df['win_deal'] = (sumary_df['count_win'].astype(float) / sumary_df['deal'].astype(float))
            sumary_df['loss_deal'] = (sumary_df['count_loss'].astype(float) / sumary_df['deal'].astype(float))
            sumary_df['hold_deal'] = (sumary_df['count_hold'].astype(float) / sumary_df['deal'].astype(float))
            sumary_df['cutloss_deal'] = (sumary_df['count_cutloss'].astype(float) / sumary_df['deal'].astype(float))
            sumary_df[rank_col] = 0.4 * sumary_df["si_return"] + 100 * (
                    0.3 * sumary_df["win_deal"] +
                    0.15 * sumary_df["win_quarter"] +
                    0.1 * sumary_df["winblock_20quarters"] +
                    0.05 * sumary_df["winblock_24months"]
            )

            # Prepare for ranking
            deal_df = deal_df.merge(sumary_df[['filter', rank_col]], on='filter', how='left')

        if rank_col == 'win_deal':
            sumary_df[rank_col] = (sumary_df['count_win'].astype(float) / sumary_df['deal'].astype(float))
            deal_df = deal_df.merge(sumary_df[['filter', rank_col]], on='filter', how='left')

        if rank_col == 'trading_daily':
            deal_df[rank_col] = deal_df['Volume'] * deal_df['Price']

        return deal_df

    if combine_weight is None:
        combine_weight = {}

    params = {
        'nums_block': force_block_nums,
        'enable_block_overheating': enable_block_overheating,

    }
    lres = evaluate_for_all(dictFilter, cutloss=CUTLOSS, **params)
    with st.spinner('⏳ Wait for calculating'):
        try:
            pd_deal = pd.concat([res[2] for res in lres if res is not None and not res[2].empty],
                                axis=0).reset_index(drop=True)
            pd_all = pd.concat([res[1] for res in lres if res is not None and not res[1].empty], axis=0). \
                sort_values('time', ascending=False).reset_index(drop=True)
            pd_b = pd.concat([res[0] for res in lres if res is not None and not res[0].empty], axis=0).reset_index(
                drop=True)
        except Exception as e:
            st.error(e)
            st.warning("Please review your filters and try again. \n At least 1 buy 1 sell pattern?")
            return [pd.DataFrame()] * 8

        # Simulate time
        start_date, end_date = parse_time(dictFilter)
        end_date = min(datetime.today(), datetime.strptime(end_date, "%Y-%m-%d")).strftime('%Y-%m-%d')

        # Process dataframe
        df_process = PreProcess()
        pd_deal = df_process.deals(pd_deal)
        pd_b = df_process.hits(pd_b)
        pdh = df_process.group_by(pd_b, ['filter'])

        _pdd_d = df_process.group_by(pd_deal, ['filter'])
        _pdd_q = df_process.group_by(pd_deal, ['filter', "quarter"])
        _pdd_q_combine = df_process.group_by(pd_deal, ["quarter"])

        pd_deal['month'] = pd_deal['time'].str[:7]
        _pdd_m = df_process.group_by(pd_deal, ['filter', "month"])
        _pdd_m_combine = df_process.group_by(pd_deal, ["month"])

        # Combine
        combined_deal = pd_deal.copy()
        combined_deal['filter'] = 'Combine'
        combined_deal = df_process.group_by(combined_deal, ['filter'])
        _pdd_d = _pdd_d._append(combined_deal)

        # latest hits dataframe
        pdy = pd_b[
            ['filter', 'ticker', 'time', 'Close', 'Volume', 'P1W', 'P1M', 'P3M', 'P6M', 'P1Y', 'P2Y', 'Sell_profit',
             'Sell_filter', 'Sell_time', 'holding_period']].copy()
        pdy.drop_duplicates(subset=['filter', 'ticker'], keep='last', inplace=True)
        pdy = pdy.sort_values('time', ascending=False).reset_index(drop=True)

        # pattern hit dataframe
        for f in pdh.columns:
            if f.startswith('spec_col_count'):
                col = f.split('_')[-1]
                pdh[f"%{col}"] = (pdh[f].astype(int) / pdh['hit'].astype(int)) * 100

        pdh["%Sell_win"] = (pdh['count_win'].astype(int) / pdh['hit'].astype(int)) * 100
        pdh["%Sell_loss"] = (pdh['count_loss'].astype(int) / pdh['hit'].astype(int)) * 100
        pdh['%Hold'] = (pdh['count_hold'].astype(int) / pdh['hit'].astype(int)) * 100
        pdh["%cutloss"] = (pdh['count_cutloss'].astype(int) / pdh['hit'].astype(int)) * 100

        pdh.insert(pdh.shape[1] - 1, 'P_Sell', pdh.pop("P_Sell"))
        pdh.insert(pdh.shape[1] - 1, 'P_Hold', pdh.pop("P_Hold"))
        pdh.insert(pdh.shape[1] - 1, 'P_cutloss', pdh.pop("P_cutloss"))
        pdh.insert(pdh.shape[1] - 1, 'P_expected', pdh.pop("Sell_profit"))
        pdh.insert(pdh.shape[1] - 1, 'Holding_period', pdh.pop("holding_period"))

        # drop unused columns
        for f in pdh.columns:
            if f.startswith('spec_col'):
                pdh.drop(f, axis=1, inplace=True)
        pdh.drop(['count_win', 'count_loss', 'count_hold', 'count_cutloss'], axis=1, inplace=True)

        # historical deals dataframe
        pdx = pd_deal[['filter', 'ticker', 'time', 'buy_price', 'sell_price', 'profit', 'sell_filter', 'sell_time',
                       'holding_period']].copy().sort_values('time', ascending=False).reset_index(drop=True)

        # data for barchart
        pd_plot = _pdd_q.pivot_table(index='quarter', columns='filter', values='deal', aggfunc='sum').reset_index()

        # win_quarter
        _pdd_d['win_quarter'] = 0
        _pdd_d['winblock_20quarters'] = 0
        tail_quarter = str(pd.to_datetime(end_date).to_period('Q') - 20)
        pd_histogram_quarter = {}
        for f in _pdd_d['filter'].unique():
            if f == 'Combine':
                data = _pdd_q_combine
            else:
                data = _pdd_q[_pdd_q['filter'] == f]
            data.rename(columns={'count_win': 'Win', 'count_loss': 'Loss', 'count_hold': 'Hold',
                                 'count_hold_win': 'Win_Hold', 'count_hold_loss': 'Loss_Hold',
                                 'count_cutloss': 'Cutloss'}, inplace=True)

            pd_histogram_quarter[f] = data[['quarter', 'Win', 'Loss', 'Win_Hold', 'Loss_Hold', 'Cutloss']]

        for filter, df in pd_histogram_quarter.items():
            # win_quarter
            win = df[(df['Win'] + df['Win_Hold']) >= (df['Loss'] + df['Cutloss'] + df['Loss_Hold'])].shape[0]
            loss = df[(df['Win'] + df['Win_Hold']) <= (df['Loss'] + df['Cutloss'] + df['Loss_Hold'])].shape[0]
            _pdd_d.loc[_pdd_d['filter'] == filter, 'win_quarter'] = win / sum([win, loss]) if (
                                                                                                      win + loss) > 0 else 0

            # df_tail = df.tail(21).iloc[:-1]
            df_tail = df[df['quarter'] >= tail_quarter]

            win_tail = df_tail[(df_tail['Win'] + df_tail['Win_Hold']) >= (
                    df_tail['Loss'] + df_tail['Cutloss'] + df_tail['Loss_Hold'])].shape[0]
            loss_tail = df_tail[(df_tail['Win'] + df_tail['Win_Hold']) <= (
                    df_tail['Loss'] + df_tail['Cutloss'] + df_tail['Loss_Hold'])].shape[0]
            _pdd_d.loc[_pdd_d['filter'] == filter, 'winblock_20quarters'] = win_tail / sum(
                [win_tail, loss_tail]) if (win_tail + loss_tail) > 0 else 0

        # winblock_24months
        _pdd_d['winblock_24months'] = 0
        tail_month = str(pd.to_datetime(end_date).to_period('M') - 24)

        pd_histogram_month = {}
        for f in _pdd_d['filter'].unique():
            if f == 'Combine':
                data = _pdd_m_combine
            else:
                data = _pdd_m[_pdd_m['filter'] == f]
            data.rename(columns={'count_win': 'Win', 'count_loss': 'Loss', 'count_hold': 'Hold',
                                 'count_hold_win': 'Win_Hold', 'count_hold_loss': 'Loss_Hold',
                                 'count_cutloss': 'Cutloss'}, inplace=True)

            pd_histogram_month[f] = data[['month', 'Win', 'Loss', 'Win_Hold', 'Loss_Hold', 'Cutloss']]

        for filter, df in pd_histogram_month.items():
            # df_tail = df.tail(24)
            df_tail = df[df['month'] >= tail_month]
            win_tail = df_tail[(df_tail['Win'] + df_tail['Win_Hold']) >= (
                    df_tail['Loss'] + df_tail['Cutloss'] + df_tail['Loss_Hold'])].shape[0]
            loss_tail = df_tail[(df_tail['Win'] + df_tail['Win_Hold']) <= (
                    df_tail['Loss'] + df_tail['Cutloss'] + df_tail['Loss_Hold'])].shape[0]

            _pdd_d.loc[_pdd_d['filter'] == filter, 'winblock_24months'] = win_tail / sum([win_tail, loss_tail]) \
                if (win_tail + loss_tail) > 0 else 0

        combines = dictFilter.get('CombinePattern', None)

        now = time.time()
        simulation = Simulation_all(start_date=start_date, end_date=end_date, num_proc=NUM_PROCESS,
                                    initial_assets=simulative_assets, max_deals=simulative_slots,
                                    combine=combines, combine_block=combine_block)
        si_cols = ['si_deals', 'si_total_time', 'si_time_in_market', 'si_cash_profit', 'si_profit', 'si_return',
                   'si_return_std', 'si_return_max', 'si_return_min', 'si_utilization', 'si_win_deal', 'si_win_quarter',
                   'si_ticker_diversity', 'si_quarter_ticker_diversity', 'si_unique_ticker', 'si_unique_quarter_ticker',
                   'si_peak_number_deals']
        si_result_cols = ['match_deals', 'total_time', 'time_in_market', 'cash_profit', 'profit', 'return',
                          'return_std', 'return_max', 'return_min', 'utilization', 'win_deal', 'win_quarter',
                          'set_ticker', 'set_quarter_ticker', 'unique_ticker', 'unique_quarter_ticker',
                          'peak_number_deals']

        if not combine_only:
            result = simulation.run_fast(df_deals=pd_deal, iterate=30, s_type=simulative_type)

            for pattern, value in result.items():
                if pattern not in _pdd_d['filter'].unique():
                    _pdd_d = _pdd_d._append({'filter': pattern}, ignore_index=True)

                for si_col, si_result_col in zip(si_cols, si_result_cols):
                    _pdd_d.loc[_pdd_d['filter'] == pattern, si_col] = value[si_result_col]

        df_combine_deals = ranking_score_func(deal_df=pd_deal, sumary_df=_pdd_d, rank_col=combine_type,
                                              weight=combine_weight)

        combine_result = simulation.run_fast(df_deals=df_combine_deals, iterate=50, s_type=simulative_type,
                                             r_col=combine_type, handle='combine')

        detail_simulation_result = simulation.get_detail(s_type=simulative_type)
        simulate_log = detail_simulation_result['log']

        logger.info(f"Simulation time: {time.time() - now}")

        for pattern, value in combine_result.items():
            if pattern not in _pdd_d['filter'].unique():
                _pdd_d = _pdd_d._append({'filter': pattern}, ignore_index=True)

            for si_col, si_result_col in zip(si_cols, si_result_cols):
                _pdd_d.loc[_pdd_d['filter'] == pattern, si_col] = value[si_result_col]

        pdd = _pdd_d[['filter', 'deal']].copy()
        pdd['si_return'] = _pdd_d['si_return']
        pdd['si_return_std'] = _pdd_d['si_return_std']
        pdd['si_ticker_diversity'] = _pdd_d['si_ticker_diversity']
        pdd['si_q_ticker_diversity'] = _pdd_d['si_quarter_ticker_diversity']
        pdd['profit_expected'] = _pdd_d['profit']

        pdd['%win_deal'] = (_pdd_d['count_win'].astype(float) / _pdd_d['deal'].astype(float)) * 100
        pdd['%loss_deal'] = (_pdd_d['count_loss'].astype(float) / _pdd_d['deal'].astype(float)) * 100
        pdd['%hold_deal'] = (_pdd_d['count_hold'].astype(float) / _pdd_d['deal'].astype(float)) * 100
        pdd['%cutloss_deal'] = (_pdd_d['count_cutloss'].astype(float) / _pdd_d['deal'].astype(float)) * 100

        pdd["%win_quarter"] = _pdd_d['win_quarter'] * 100
        pdd["%winblock_20quarters"] = _pdd_d['winblock_20quarters'] * 100
        pdd["%winblock_24months"] = _pdd_d['winblock_24months'] * 100
        pdd["ranking_point"] = 0.4 * pdd["si_return"] + \
                               0.3 * pdd["%win_deal"] + \
                               0.15 * pdd["%win_quarter"] + \
                               0.1 * pdd["%winblock_20quarters"] + \
                               0.05 * pdd["%winblock_24months"]
        # 0.1 * pdd["deal"] / pdd["deal"].nlargest(2).values[1] * 100

        pdd['profit_win'] = _pdd_d['p_win']
        pdd['profit_loss'] = _pdd_d['p_loss']
        pdd['profit_hold'] = _pdd_d['p_hold']
        pdd['profit_cutloss'] = _pdd_d['p_cutloss']
        pdd['holding_period'] = _pdd_d['holding_period']
        pdd['profit_vni'] = _pdd_d['profit_vni']
        pdd['corr_deal_vni'] = _pdd_d['corr']
        # pdd['n_half'] = _pdd_d['n_half']
        # pdd['entropy'] = _pdd_d['entropy']
        pdd['si_total_time'] = _pdd_d['si_total_time']
        pdd['si_time_in_market'] = _pdd_d['si_time_in_market']
        pdd['si_deals'] = _pdd_d['si_deals']
        pdd['si_profit'] = _pdd_d['si_profit']
        pdd['si_cash_profit'] = _pdd_d['si_cash_profit']
        pdd['si_utilization'] = _pdd_d['si_utilization']
        pdd['si_%win_deal'] = _pdd_d['si_win_deal']
        pdd['si_%win_quarter'] = _pdd_d['si_win_quarter']
        pdd['si_unique_q_tickers'] = _pdd_d['si_unique_quarter_ticker']
        pdd['si_peak_number_deals'] = _pdd_d['si_peak_number_deals']
        pdd['si_return_max'] = _pdd_d['si_return_max']
        pdd['si_return_min'] = _pdd_d['si_return_min']

        now = (datetime.now(timezone.utc).strftime("%Y-%m-%d:%H:%M:%S"))
        logger.info(
            f"## {now} -- Sum P_performance: {(pdd['si_return'] * pdd['si_deals']).sum() / pdd['si_deals'].sum()}")
        return pdd, pdh, pdx, pdy, pd_all, pd_plot, pd_histogram_quarter, simulate_log


@memory.cache
def eval_filter_shortsell(dictFilter, CUTLOSS=0.1):
    """ Return dataframe of profit
        pdp: dataframe of profit
        pdx: dataframe of historical hit
        pdy: dataframe of latest hit
    """

    lres = evaluate_for_shortsell(dictFilter, cutloss=CUTLOSS)
    with st.spinner('⏳ Wait for calculating'):

        try:
            pd_short = pd.concat([res[0] for res in lres if res is not None and not res[0].empty],
                                 axis=0).reset_index(drop=True)
            pd_short_all = pd.concat([res[1] for res in lres if res is not None and not res[1].empty],
                                     axis=0).reset_index(drop=True)

        except Exception as e:
            st.error(e)
            st.warning("Please review your filters and try again. \n"
                       "At least 1 buy 1 sell pattern?")
            return [pd.DataFrame()] * 4

        # Simulate time
        start_date, end_date = parse_time(dictFilter)
        end_date = min(datetime.today(), datetime.strptime(end_date, "%Y-%m-%d")).strftime('%Y-%m-%d')

        # Process dataframe
        d_agg = {'trading_val': "sum", 'p_trading_val': "sum", 'trading_val_clip': "sum", 'p_trading_val_clip': "sum",
                 'P1W': "median", 'P2W': "median", 'P3W': "median", 'P1M': "median", 'P2M': "median", 'P3M': "median",
                 'P6M': "median", 'P1Y': "median", 'P2Y': "median", 'median_profit': "median"}
        df_process = PreProcess()
        pd_short = df_process.shortsell(pd_short)
        _pds_d = df_process.group_by(pd_short, ['filter'], d_agg=d_agg)
        _pdd_q = df_process.group_by(pd_short, ['filter', "quarter"], d_agg=d_agg)
        pd_short['month'] = pd_short['time'].str[:7]
        _pdd_m = df_process.group_by(pd_short, ['filter', "month"], d_agg=d_agg)

        # data for barchart
        # pd_plot = _pdd_q.pivot_table(index='quarter', columns='filter', values='deal', aggfunc='sum').reset_index()

        # win_quarter
        _pds_d['win_quarter'] = 0
        _pds_d['winblock_20quarters'] = 0
        tail_quarter = str(pd.to_datetime(end_date).to_period('Q') - 20)
        pd_histogram_quarter = {}
        for f in _pdd_q['filter'].unique():
            data = _pdd_q[_pdd_q['filter'] == f]
            data.rename(columns={'count_win': 'Win', 'count_loss': 'Loss', 'count_hold': 'Hold',
                                 'count_hold_win': 'Win_Hold', 'count_hold_loss': 'Loss_Hold',
                                 'count_cutloss': 'Cutloss'}, inplace=True)

            pd_histogram_quarter[f] = data[['quarter', 'Win', 'Loss', 'Win_Hold', 'Loss_Hold', 'Cutloss']]

        for filter, df in pd_histogram_quarter.items():
            win = df[(df['Win'] + df['Win_Hold']) >= (df['Loss'] + df['Cutloss'] + df['Loss_Hold'])].shape[0]
            loss = df[(df['Win'] + df['Win_Hold']) <= (df['Loss'] + df['Cutloss'] + df['Loss_Hold'])].shape[0]

            _pds_d.loc[_pds_d['filter'] == filter, 'win_quarter'] = win / sum([win, loss]) \
                if (win + loss) > 0 else 0

            # df_tail = df.tail(21).iloc[:-1]
            df_tail = df[df['quarter'] >= tail_quarter]

            win_tail = df_tail[(df_tail['Win'] + df_tail['Win_Hold']) >= (
                    df_tail['Loss'] + df_tail['Cutloss'] + df_tail['Loss_Hold'])].shape[0]
            loss_tail = df_tail[(df_tail['Win'] + df_tail['Win_Hold']) <= (
                    df_tail['Loss'] + df_tail['Cutloss'] + df_tail['Loss_Hold'])].shape[0]
            _pds_d.loc[_pds_d['filter'] == filter, 'winblock_20quarters'] = win_tail / sum(
                [win_tail, loss_tail]) if (win_tail + loss_tail) > 0 else 0

        # winblock_24months
        _pds_d['winblock_24months'] = 0
        tail_month = str(pd.to_datetime(end_date).to_period('M') - 24)
        pd_histogram_month = {}
        for f in _pdd_m['filter'].unique():
            data = _pdd_m[_pdd_m['filter'] == f]
            data.rename(columns={'count_win': 'Win', 'count_loss': 'Loss', 'count_hold': 'Hold',
                                 'count_hold_win': 'Win_Hold', 'count_hold_loss': 'Loss_Hold',
                                 'count_cutloss': 'Cutloss'}, inplace=True)

            pd_histogram_month[f] = data[['month', 'Win', 'Loss', 'Win_Hold', 'Loss_Hold', 'Cutloss']]

        for filter, df in pd_histogram_month.items():
            # df_tail = df.tail(24)
            df_tail = df[df['month'] >= tail_month]

            win_tail = df_tail[(df_tail['Win'] + df_tail['Win_Hold']) >= (
                    df_tail['Loss'] + df_tail['Cutloss'] + df_tail['Loss_Hold'])].shape[0]
            loss_tail = df_tail[(df_tail['Win'] + df_tail['Win_Hold']) <= (
                    df_tail['Loss'] + df_tail['Cutloss'] + df_tail['Loss_Hold'])].shape[0]

            _pds_d.loc[_pds_d['filter'] == filter, 'winblock_24months'] = win_tail / sum([win_tail, loss_tail]) \
                if (win_tail + loss_tail) > 0 else 0

        pds = _pds_d[['filter', 'deal']].copy()
        pds['profit_expected'] = _pds_d['profit'].values
        pds['median_profit'] = _pds_d['median_profit'].values
        pds['weighted_profit'] = _pds_d['p_trading_val'] / _pds_d['trading_val'] * 100
        pds['%win_deal'] = (_pds_d['count_win'].astype(int) / _pds_d['deal'].astype(int)) * 100
        pds['%loss_deal'] = (_pds_d['count_loss'].astype(int) / _pds_d['deal'].astype(int)) * 100
        pds['%hold_deal'] = (_pds_d['count_hold'].astype(int) / _pds_d['deal'].astype(int)) * 100
        pds["%cutloss_deal"] = (_pds_d['count_cutloss'].astype(int) / _pds_d['deal'].astype(int)) * 100
        pds["%win_quarter"] = _pds_d['win_quarter'] * 100
        pds["%winblock_20quarters"] = _pds_d['winblock_20quarters'] * 100
        pds["%winblock_24months"] = _pds_d['winblock_24months'] * 100
        pds["trading_val"] = _pds_d['trading_val']
        pds["p_trading_val"] = _pds_d['p_trading_val']
        pds["weighted_profit_clip"] = _pds_d['p_trading_val_clip'] / _pds_d['trading_val_clip']
        pds[['P1W', 'P2W', 'P3W', 'P1M', 'P2M', 'P3M', 'P6M', 'P1Y', 'P2Y']] = _pds_d[
            ['P1W', 'P2W', 'P3W', 'P1M', 'P2M', 'P3M', 'P6M', 'P1Y', 'P2Y']].values

        pds['profit_win'] = _pds_d['p_win'].values
        pds['profit_loss'] = _pds_d['p_loss'].values
        pds['profit_hold'] = _pds_d['p_hold'].values
        pds['profit_cutloss'] = _pds_d['p_cutloss'].values
        pds['holding_period'] = _pds_d['holding_period'].values
        pds['profit_vni'] = _pds_d['profit_vni'].values
        pds['corr_deal_vni'] = _pds_d['corr'].values

        # historical short deal
        pd_short['weight_before_clip'] = (pd_short['Price'] * pd_short['Volume'] / pd_short['Trading_Session'])
        pd_short['weight_clip'] = (pd_short['Price'] * pd_short['Volume'] / pd_short['Trading_Session']).clip(
            upper=0.01)

        pd_sd = pd_short[['filter', 'ticker', 'time', 'sell_price', 'buy_price', 'profit', 'buy_filter', 'buy_time',
                          'holding_period', 'trading_val', 'p_trading_val', 'weight_before_clip',
                          'weight_clip']].sort_values(
            'time', ascending=False).reset_index(drop=True)

        now = (datetime.now(timezone.utc).strftime("%Y-%m-%d:%H:%M:%S"))
        logger.info(
            f"## {now} -- Sum P_performance: {(pds['profit_expected'] * pds['deal']).sum() / pds['deal'].sum()}")
        return pds, pd_sd, pd_short_all, pd_histogram_quarter


# Function to plot candlestick and RSI
def plot_stock_data(df, dictFilter: dict, indicators: list):
    """
    df :
    - index 0..n
    - ymd : datetime
    - time : str 2021-01-01
    - indicator : float
    """
    nrows = 6
    fig = sp.make_subplots(rows=nrows, cols=1, shared_xaxes=True, vertical_spacing=0.05,
                           subplot_titles=('Price', 'Volume', 'RSI', 'Daily CMB', 'Weekly CMB', 'Monthly CMB'),
                           row_heights=[0.5, 0.18, 0.2, 0.18, 0.18, 0.18]
                           )

    row = 1
    # Candlestick subplot
    candlestick = go.Candlestick(x=df['ymd'],
                                 open=df['Open'],
                                 high=df['High'],
                                 low=df['Low'],
                                 close=df['Close'],
                                 name='Candlestick')

    fig.add_trace(candlestick, row=row, col=1)
    fig.add_trace(go.Scatter(x=df['ymd'], y=df['MA200'], mode='lines', name='MA200'), row=row, col=1)
    for indicator in indicators:
        try:
            fig.add_trace(go.Scatter(x=df['ymd'], y=df[indicator], mode='lines', name=indicator), row=row, col=1)
        except Exception as e:
            pass

    row += 1
    fig.add_trace(go.Bar(x=df['ymd'], y=df['Volume'], showlegend=False), row=row, col=1)
    # Adjust y-axis range for Candlestick subplot based on x_range

    # RSI subplot
    row += 1
    fig.add_trace(go.Scatter(x=df['ymd'], y=df['D_RSI'], mode='lines', name='RSI'), row=row, col=1)

    # Display Filter
    for f in [k for k in dictFilter if (k.startswith('_') or k.startswith('~'))]:
        try:
            fig.add_trace(
                go.Scatter(x=df['ymd'], y=apply_filter(df, dictFilter[f]).astype(float) * 0.9, mode='lines', name=f[1:],
                           opacity=0.5, line=dict(width=3)), row=row, col=1)
        except Exception as e:
            print(e)
    fig.update_yaxes(range=[0, 1], row=row, col=1)

    COLOR = {'D_CMB': 'rgba(128, 0, 0, .8)', 'D_CMB_Fast': 'rgba(128, 0, 0, 0.5)', 'D_CMB_Slow': 'rgba(128, 0, 0, 0.2)',
             'W_CMB': 'rgba(0, 128, 0, .8)', 'W_CMB_Fast': 'rgba(0, 128, 0, 0.5)', 'W_CMB_Slow': 'rgba(0, 128, 0, 0.2)',
             'M_CMB': 'rgba(0, 0, 128, .8)', 'M_CMB_Fast': 'rgba(0, 0, 128, 0.5)', 'M_CMB_Slow': 'rgba(0, 0, 128, 0.2)',
             }
    row += 1
    # CMB daily
    for col in ['D_CMB', 'D_CMB_Fast', 'D_CMB_Slow']:
        ii = df[col] == df[col]
        fig.add_trace(go.Scatter(x=df[ii]['ymd'], y=df[ii][col], mode='lines', name=col,
                                 line=dict(width=2, color=COLOR.get(col, None))), row=row, col=1)
    fig.update_yaxes(range=[-1, 2], row=row, col=1)
    row += 1
    # CMB weekly
    for col in ['W_CMB', 'W_CMB_Fast', 'W_CMB_Slow']:
        ii = df[col] == df[col]
        fig.add_trace(go.Scatter(x=df[ii]['ymd'], y=df[ii][col], mode='lines', name=col,
                                 line=dict(width=2, color=COLOR.get(col, None))), row=row, col=1)
    # divergence
    # Bullish
    PERIOD = 5
    pdxx = df[(df['W_CMB_Step'] > 0) & (df['W_CMB_LAG'] == 1)].copy()

    for i in range(pdxx.shape[0]):
        step, len = pdxx.iloc[i]['W_CMB_Step'], pdxx.iloc[i]['W_CMB_LEN']
        if i - PERIOD < 0:
            continue
        i = i - PERIOD
        ymd2 = pdxx.iloc[i]['ymd']

        # ymd2, step, len = pdxx.iloc[i]['ymd'], pdxx.iloc[i]['W_CMB_Step'], pdxx.iloc[i]['W_CMB_LEN']
        j2 = pdxx.index[i]
        j1 = int(j2 - (len) * PERIOD)
        if j1 < 0:
            continue
        ymd1 = df.iloc[j1]['ymd']
        y2 = df.iloc[j2]['W_CMB']
        y1 = y2 - step * (len)
        pdyy = pd.DataFrame({'ymd': [ymd1, ymd2], 'trend': [y1, y2]})
        fig.add_trace(go.Scatter(x=pdyy['ymd'], y=pdyy['trend'], mode='lines', showlegend=False,
                                 line=dict(color='rgba(190, 144, 212, .5)')), row=row, col=1)
    # Bearish
    pdxx = df[(df['W_CMB_Step'] < 0) & (df['W_CMB_LAG'] == 1)].copy()

    for i in range(pdxx.shape[0]):
        step, len = pdxx.iloc[i]['W_CMB_Step'], pdxx.iloc[i]['W_CMB_LEN']
        if i - PERIOD < 0:
            continue
        i = i - PERIOD
        ymd2 = pdxx.iloc[i]['ymd']

        # ymd2, step, len = pdxx.iloc[i]['ymd'], pdxx.iloc[i]['W_CMB_Step'], pdxx.iloc[i]['W_CMB_LEN']
        j2 = pdxx.index[i]
        j1 = int(j2 - (len) * PERIOD)
        if j1 < 0:
            continue
        ymd1 = df.iloc[j1]['ymd']
        y2 = df.iloc[j2]['W_CMB']
        y1 = y2 - step * (len)
        pdyy = pd.DataFrame({'ymd': [ymd1, ymd2], 'trend': [y1, y2]})
        fig.add_trace(go.Scatter(x=pdyy['ymd'], y=pdyy['trend'], mode='lines', showlegend=False,
                                 line=dict(color='rgba(242, 38, 19, .5)')), row=row, col=1)

    fig.update_yaxes(range=[-1, 2], row=row, col=1)

    row += 1
    # CMB monthly
    for col in ['M_CMB']:
        ii = df[col] == df[col]
        fig.add_trace(go.Scatter(x=df[ii]['ymd'], y=df[ii][col], mode='lines', name=col,
                                 line=dict(width=2, color=COLOR.get(col, None))), row=row, col=1)
    # divergence
    # Bullish
    PERIOD = 20
    pdxx = df[(df['M_CMB_Step'] > 0) & (df['M_CMB_LAG'] == 1)].copy()

    for i in range(pdxx.shape[0]):
        step, len = pdxx.iloc[i]['M_CMB_Step'], pdxx.iloc[i]['M_CMB_LEN']
        if i - PERIOD < 0:
            continue
        i = i - PERIOD
        ymd2 = pdxx.iloc[i]['ymd']

        # ymd2, step, len = pdxx.iloc[i]['ymd'], pdxx.iloc[i]['M_CMB_Step'], pdxx.iloc[i]['M_CMB_LEN']
        j2 = pdxx.index[i]
        j1 = int(j2 - (len) * PERIOD)
        if j1 < 0:
            continue
        ymd1 = df.iloc[j1]['ymd']
        y2 = df.iloc[j2]['M_CMB']
        y1 = y2 - step * len
        pdyy = pd.DataFrame({'ymd': [ymd1, ymd2], 'trend': [y1, y2]})
        # print(i,step,pdyy)
        fig.add_trace(go.Scatter(x=pdyy['ymd'], y=pdyy['trend'], mode='lines', showlegend=False,
                                 line=dict(color='rgba(190, 144, 212, .5)')), row=row, col=1)

    # Bearish
    pdxx = df[(df['M_CMB_Step'] < 0) & (df['M_CMB_LAG'] == 1)].copy()

    for i in range(pdxx.shape[0]):
        step, len = pdxx.iloc[i]['M_CMB_Step'], pdxx.iloc[i]['M_CMB_LEN']
        if i - PERIOD < 0:
            continue
        i = i - PERIOD
        ymd2 = pdxx.iloc[i]['ymd']

        # ymd2, step, len = pdxx.iloc[i]['ymd'], pdxx.iloc[i]['M_CMB_Step'], pdxx.iloc[i]['M_CMB_LEN']
        j2 = pdxx.index[i]
        j1 = int(j2 - (len) * PERIOD)
        if j1 < 0:
            continue
        ymd1 = df.iloc[j1]['ymd']
        y2 = df.iloc[j2]['M_CMB']
        y1 = y2 - step * len
        pdyy = pd.DataFrame({'ymd': [ymd1, ymd2], 'trend': [y1, y2]})
        # print(i,step,pdyy)
        fig.add_trace(go.Scatter(x=pdyy['ymd'], y=pdyy['trend'], mode='lines', showlegend=False,
                                 line=dict(color='rgba(242, 38, 19, .5)')), row=row, col=1)
    fig.update_yaxes(range=[-1, 2], row=row, col=1)

    # Show grid and set figure size
    for r in range(nrows):
        fig.update_xaxes(showgrid=True, showticklabels=True, row=r + 1, col=1)

    fig.update_yaxes(fixedrange=False)
    fig.update_layout(
        height=1200,
        width=1500,
        xaxis_rangeslider_visible=False
    )

    return fig


# Load custom filters from config.json
def load_custom_filters():
    custom_filters = {}
    if os.path.exists("config.json"):
        with open("config.json", "r") as config_file:
            custom_filters = json.load(config_file)
    return custom_filters


# Save custom filters to config.json
def save_custom_filters(custom_filters):
    with open("config.json", "w") as config_file:
        json.dump(custom_filters, config_file)


def parse_custom_filters(f, params: dict = None, weight: str = None, combine: dict = None):
    """Create a dict for save filters with keys: filter, weight, params"""
    if weight is None or not isinstance(weight, str):
        weight = '{}'
    if params is None:
        params = {}
    if combine is None or not isinstance(combine, dict):
        combine = {}

    d_params = {
        'cutloss': int(params.get('cutloss', 15)),
        'si_type': params.get('si_type', 'Full cash fix slot'),
        'si_slot': int(params.get('si_slot', 10)),
        'si_asset': float(params.get('si_asset', 10.0)),
        'w_lookback': int(params.get('w_lookback', 10)),
        'w_thres_buy': float(params.get('w_thres_buy', 1.0)),
        'w_thres_sell': float(params.get('w_thres_sell', -1.0)),
        'w_k_exp': float(params.get('w_k_exp', 0.0)),
        'co_rank': params.get('co_rank', 'ranking_point'),

    }

    try:
        new_filter = {
            "filter": f,
            "weight": weight,
            "params": d_params,
            "combine": combine
        }
    except Exception as e:
        raise ValueError(f"Error creating filter: {str(e)}")

    return new_filter


def get_selected_filters(selected_filter, custom_filters):
    """
    Get the selected filters from the filters list.
    """
    try:
        if selected_filter not in custom_filters:
            raise KeyError(f"Selected filter '{selected_filter}' not found in custom filters")

        filter_data = custom_filters[selected_filter]

        if not isinstance(filter_data, dict):
            raise ValueError(f"Invalid filter data format for '{selected_filter}'")

        custom_filter_text = filter_data.get('filter')
        filter_weight_text = filter_data.get('weight')
        filter_params = filter_data.get('params')
        filter_combine = filter_data.get('combine', {})

    except (KeyError, ValueError) as e:

        new_filter = parse_custom_filters(f=custom_filters[selected_filter][0],
                                          weight=custom_filters[selected_filter][-1])
        custom_filters[selected_filter] = new_filter
        save_custom_filters(custom_filters)

        custom_filter_text = new_filter['filter']
        filter_weight_text = new_filter['weight']
        filter_params = new_filter['params']
        filter_combine = new_filter['combine']

    return custom_filter_text, filter_weight_text, filter_params, filter_combine


def get_define_combine_weight(combine_text, filter_combine):
    """
    Get the define combine weight from the weight text.
    """
    d_weight = json.loads(filter_combine.get(combine_text, "{}"))

    weights = {
        'technical_point': {
            "D_RSI/D_RSI_T1W>1": "w_1",
            "Close/VAP1W>1": "w_2",
            "D_MFI/D_MFI_T1W>1": "w_3",
            "D_MACD/D_MACD_T1W>1": "w_4",
            "0.97<Close/LO_3M_T1>1.2": "w_5",
            "Volume/Volume_1M>1": "w_6",
            "D_RSI_Max1W/D_RSI_Max3M>1": "w_7",
            "bias": "w_8"
        },
        'order_point': {
            "BKMA200": "w_1",
            "TrendingGrowth": "w_2",
            "TL3M": "w_3",
            "BuySupport": "w_4",
            "RSILow30": "w_5",
            "UnderBV": "w_6",
            "SuperGrowth": "w_7",
            "SurpriseEarning": "w_8",
            "Conservative": "w_9",
            "BullDvg": "w_10",
            "VolMax1Y": "w_11",
            "T3P4": "w_12"
        },
        'synthetic': {
            "D_RSI/D_RSI_T1W>1": "w_1",
            "Close/VAP1W>1": "w_2",
            "D_MFI/D_MFI_T1W>1": "w_3",
            "D_MACD/D_MACD_T1W>1": "w_4",
            "0.97<Close/LO_3M_T1>1.2": "w_5",
            "Volume/Volume_1M>1": "w_6",
            "D_RSI_Max1W/D_RSI_Max3M>1": "w_7",
            "FSCORE/9": "w_8",
            "BKMA200": "w_9",
            "BullDvg": "w_10",
            "BuySupport": "w_11",
            "Conservative": "w_12",
            "RSILow30": "w_13",
            "SuperGrowth": "w_14",
            "SurpriseEarning": "w_15",
            "TL3M": "w_16",
            "TrendingGrowth": "w_17",
            "UnderBV": "w_18",
            "VolMax1Y": "w_19",
            "T3P4": "w_20",
            "bias": "w_21",
        }
    }

    weight = weights.get(combine_text, {})

    filter_scores = pd.DataFrame({
        "Name": list(weight.keys()),
        "Weight": [d_weight.get(v, 0.0) for v in weight.values()]
    })

    return filter_scores, d_weight


def to_excel(df):
    output = BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, sheet_name='Sheet1')
    processed_data = output.getvalue()
    return processed_data


# Main Streamlit app
# @st.dialog("📈 Trading Log Visualization")
def show_log_dashboard(log_data):
    st.title("Trading Log Visualization Dashboard")

    # path = 'webui/'
    # # Tìm file log mới nhất
    # log_files = [path + f for f in os.listdir(path) if f.endswith('.jsonl')]
    # if not log_files:
    #     st.error("Không tìm thấy file log nào!")
    #     return
    #
    # latest_log = max(log_files, key=os.path.getctime)

    # Khởi tạo PlotService
    plot_service = PlotService(log_data)

    # Hiển thị thống kê tổng quan
    st.header("Trading Statistics")
    stats = plot_service.get_trade_statistics()

    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.metric("Total Deals", stats['total_trades'])
        st.metric("Win Rate", f"{stats['win_rate']:.2f}%")
    with col2:
        st.metric("Winning Trades", stats['winning_trades'])
        st.metric("Losing Trades", stats['losing_trades'])
    with col3:
        st.metric("Avg Profit", f"{stats['avg_profit']:.2f}%")
        st.metric("Max Profit", f"{stats['max_profit']:.2f}%")
    with col4:
        st.metric("Min Profit", f"{stats['min_profit']:.2f}%")
        st.metric("Avg Holding Period", f"{stats['avg_holding_period']:.1f} days")

    # Hiển thị biểu đồ tổng hợp
    st.header("Trading Summary")
    st.plotly_chart(plot_service.plot_trade_summary(), use_container_width=True)

    # Hiển thị các biểu đồ chi tiết
    st.header("Detailed Analysis")
    tab1, tab2 = st.tabs([
        "Profit Distribution",
        "Holding Period"
    ])

    with tab1:
        st.plotly_chart(plot_service.plot_profit_distribution(), use_container_width=True)

    with tab2:
        st.plotly_chart(plot_service.plot_holding_period_distribution(), use_container_width=True)


def main():
    # Set the page configuration
    st.set_page_config(
        page_title="Wide Streamlit App",
        page_icon=":chart_with_upwards_trend:",
        layout="wide",  # Use the "wide" layout option
        initial_sidebar_state="collapsed"  # You can set the initial state of the sidebar
    )

    with open('core_utils/env/authen-config.yaml') as file:
        authen_config = yaml.load(file, Loader=SafeLoader)

    # Pre-hashing all plain text passwords once
    # stauth.Hasher.hash_passwords(authen_config['credentials'])

    authenticator = stauth.Authenticate(
        authen_config['credentials'],
        authen_config['cookie']['name'],
        authen_config['cookie']['key'],
        authen_config['cookie']['expiry_days']
    )

    try:
        authenticator.login()
    except Exception as e:
        st.error(e)

    # Authenticating user
    if st.session_state['authentication_status']:

        authenticator.logout()
        # list of stock files
        stock_files = os.listdir(FPATH)
        stock_files = [f[:-4] for f in stock_files if f.endswith('.csv')]

        # Load custom filters from config.json
        custom_filters = load_custom_filters()

        stf = st
        _col0, _col = stf.columns([4, 1])
        with _col0:
            options = [":red[**Evaluate mode**]", ":red[**Weighted evaluate mode**]", ":red[**Short sell mode**]"]
            mode = st.pills("Mode", options, selection_mode="single", default=":red[**Evaluate mode**]",
                            label_visibility='hidden')

        with _col:
            if st.button("Reboot"):
                subprocess.call("./cron/do_st_restart.sh", shell=True)

        if mode == ':red[**Evaluate mode**]':
            st_evaluate_all(stock_files, custom_filters)
        elif mode == ':red[**Short sell mode**]':
            st_evaluate_shortsell(stock_files, custom_filters)
        else:
            st_evaluate_by_weight(stock_files, custom_filters)

        st.title('Indicator dictionary')
        st.dataframe(dict(sorted(DICTIONARY.items())), height=700, width=1200)
        gc.collect()


    elif st.session_state['authentication_status'] is False:
        st.error('Username/password is incorrect')
    elif st.session_state['authentication_status'] is None:
        st.warning('Please enter your username and password')


def st_evaluate_all(stock_files, custom_filters):
    stf = st
    selected_filter = stf.selectbox("**Select a custom filter:**", list(custom_filters.keys()))

    custom_filter_text, filter_weight_text, filter_params, filter_combine = get_selected_filters(selected_filter,
                                                                                                 custom_filters)

    # Session State management
    st.session_state.eval_done = True
    # Check selected filter in session state
    if not st.session_state.get("current_filter") == selected_filter \
            or not st.session_state.get("current_eval_type") == "evaluate_all":
        st.session_state.current_eval_type = "evaluate_all"

        st.session_state.current_filter = selected_filter
        st.session_state.slider_cutloss = filter_params['cutloss']
        st.session_state.ratio_si_type = filter_params['si_type']
        st.session_state.num_si_slot = filter_params['si_slot']
        st.session_state.num_si_asset = filter_params['si_asset']
        st.session_state.num_lookback = filter_params['w_lookback']
        st.session_state.num_thres_buy = filter_params['w_thres_buy']
        st.session_state.num_thres_sell = filter_params['w_thres_sell']
        st.session_state.num_k_exp = filter_params['w_k_exp']
        st.session_state.selectbox_co_rank = filter_params['co_rank']

        # clear old eval data
        try:
            for key in ['df_histogram', 'df_deal', 'df_hit', 'df_hístorical_deal', 'df_latest_hit', 'df_distribution',
                        'si_log']:
                if key in st.session_state.keys():
                    del st.session_state[key]
        except ValueError as e:
            stf.error(f"Error: {e}")
    col0, col1, col2 = stf.columns([2, 2, 3])

    with col2:
        combine_block = st.checkbox('Run block buys for combined simulations', False)
        if combine_block:
            @st.dialog("Show weight")
            def edit_combine_block():
                st.data_editor(
                    {
                        "Score": ['w1', 'w2', 'w3', 'w4'],
                        "Threshold": [2, 3, 4, 5]
                    }
                )

            if st.button("Show threshold"):
                edit_combine_block()

    with col1:
        combine_checked = st.checkbox('Run only combined simulations', False)
        if combine_checked:
            selected_rank_col = stf.selectbox("**Select a combined ranking:**",
                                              ['synthetic', 'technical_point', 'order_point', 'weight_point',
                                               "win_deal", 'trading_daily', 'FSCORE'],
                                              key="selectbox_co_rank")
        else:

            selected_rank_col = stf.selectbox("**Select a combined ranking:**",
                                              ['synthetic', 'technical_point', 'order_point', 'weight_point',
                                               "win_deal", 'trading_daily', 'FSCORE', 'ranking_point'],
                                              key="selectbox_co_rank")

        @st.dialog("Edit weight")
        def edit_weights(filter_scores, rank_col):

            edited_df = st.data_editor(
                filter_scores,
                num_rows="fixed",
                use_container_width=True,
                key="data_editor_dialog"
            )

            if st.button("Save"):
                filter_data = custom_filters[selected_filter]
                if 'combine' not in filter_data:
                    filter_data['combine'] = {}
                filter_data['combine'][rank_col] = json.dumps({f'w_{i + 1}': float(edited_df.loc[i, 'Weight']) for i in
                                                               range(len(edited_df))})
                custom_filters[selected_filter] = filter_data
                save_custom_filters(custom_filters)
                st.rerun()

        filter_scores_select, combine_weight = get_define_combine_weight(selected_rank_col, filter_combine)
        if not filter_scores_select.empty:
            if st.button("Show combined weight"):
                edit_weights(filter_scores_select, selected_rank_col)

    with col0:
        s_type = st.radio(
            "**Select an simulation type**",
            ["Full cash fix slot", "Full cash not fix slot", "Cash allocation"],
            key='ratio_si_type',
        )
        if s_type == 'Full cash fix slot':
            simulation_type = SIMULATE_FULL_CASH
        elif s_type == 'Full cash not fix slot':
            simulation_type = SIMULATE_FULL_CASH_NOT_FIX
        elif s_type == 'Cash allocation':
            simulation_type = SIMULATE_ALLOCATE

    def sdict2df(s):
        d = json.loads(s)
        d = collections.OrderedDict(sorted(d.items()))
        pdx = pd.DataFrame({'filter': list(d.keys()), 'value': list(d.values())})
        pdx['filter'] = pdx['filter'].astype(str)
        pdx['value'] = pdx['value'].astype(str)
        return pdx

    def df2sdict(df):
        return json.dumps(dict(zip(df['filter'], df['value'])))

    edited_df = stf.data_editor(sdict2df(custom_filter_text), hide_index=True, num_rows='dynamic',
                                use_container_width=True)
    custom_filter_text = df2sdict(edited_df)
    dictFilter = preprocess_dictFilter(custom_filter_text)

    # Check edited in session state
    if "df_value" not in st.session_state or not edited_df.equals(st.session_state["df_value"]):
        # This will only run if
        # 1. Some widget has been changed (including the dataframe editor), triggering a
        # script rerun, and
        # 2. The new dataframe value is different from the old value
        # Check logical error
        try:
            if "Init" not in dictFilter.keys():
                k = "Init"
                raise ValueError('Missing "Init" key in the filter')

            for k, v in dictFilter.items():
                check_parentheses_balance(v)
                check_logical_operators(v)
                check_comparison_operators(v)

            # clear old eval data
            for key in ['df_histogram', 'df_deal', 'df_hit', 'df_hístorical_deal', 'df_latest_hit', 'df_distribution',
                        'si_log']:
                if key in st.session_state.keys():
                    del st.session_state[key]

        except ValueError as e:
            stf.error(f"Error: {k}: {e}")

        st.session_state["df_value"] = edited_df

    # Create a layout with two columns
    col1, col2, col0 = stf.columns(3)
    with col0:
        CUTLOSS = stf.slider("Cutloss %", 1, 100, key="slider_cutloss", value=filter_params.get('cutloss')) / 100.

        _, col_1, col_2, _ = stf.columns([0.1, 1, 1, 0.1])
        with col_1:
            si_slots = stf.number_input("Simulative slots", min_value=1, max_value=100,
                                        value=filter_params.get('si_slot'), step=1,
                                        key="num_si_slot")
            force_block_nums = stf.number_input("Force block dates", min_value=0, max_value=365,
                                        value=30, step=1,
                                        key="force_block_nums")

            enable_block_overheating = stf.checkbox('Block buying when overheating', False)

        with col_2:
            si_assets = stf.number_input("Simulative assets (unit: 1 billion)", min_value=0.0, max_value=20000.0,
                                         value=filter_params.get('si_asset'), step=0.1, key="num_si_asset")
            si_assets *= 1e9

    # Save and overwrite the selected custom filter
    with col1:
        new_filter_name = stf.text_input("Enter a name if you want to save as a new profile",
                                         placeholder="Fill in here")

        broadcast = 0
        profile_col, profile_col1 = stf.columns(2)
        with profile_col:
            if stf.button("Save Profile"):
                # Create a new custom filter
                new_filter = parse_custom_filters(f=custom_filter_text,
                                                  weight=filter_weight_text,
                                                  combine=filter_combine,
                                                  params={
                                                      'cutloss': st.session_state.slider_cutloss,
                                                      'si_type': st.session_state.ratio_si_type,
                                                      'si_slot': st.session_state.num_si_slot,
                                                      'si_asset': st.session_state.num_si_asset,
                                                      "w_lookback": filter_params['w_lookback'],
                                                      "w_thres_buy": filter_params['w_thres_buy'],
                                                      "w_thres_sell": filter_params['w_thres_sell'],
                                                      "w_k_exp": filter_params['w_k_exp'],
                                                      "co_rank": st.session_state.selectbox_co_rank,
                                                  }
                                                  )

                if new_filter_name and (new_filter_name not in custom_filters):
                    custom_filters[new_filter_name] = new_filter
                    broadcast = 1
                else:
                    # Overwrite the existing custom filter
                    custom_filters[selected_filter] = new_filter
                    broadcast = 2

                # Save the updated custom filters to config.json
                save_custom_filters(custom_filters)

        with profile_col1:
            @st.dialog("Do you want to delete this profile?")
            def vote(selected_filter):
                st.write(f"Are you sure you want to delete '{selected_filter}' profile?")
                if st.button("OK"):
                    del custom_filters[selected_filter]
                    save_custom_filters(custom_filters)
                    st.rerun()

            if stf.button("Delete Profile"):
                vote(selected_filter)

        if broadcast == 1:
            stf.success(f"Profile '{new_filter_name}' saved successfully!")
        elif broadcast == 2:
            stf.success(f"Profile '{selected_filter}' updated successfully!")

    # with col2:
    #     if stf.button("Export all filters"):
    #         ss = json.dumps(custom_filters)
    #         # print(ss)
    #         stf.text_area("config.json", ss)

    st.markdown('######')
    download_checked = st.checkbox('Download deals data', False)

    if stf.button("**Evaluate and Search**"):

        logger.info("START **Evaluate and Search**")
        logger.info(f"{selected_filter}: {dictFilter}")

        ppd, pdp, pdx, pd_latest_hit, pd_all, pd_plot, pd_histogram, si_log = eval_filter_all(dictFilter,
                                                                                              CUTLOSS=CUTLOSS,
                                                                                              simulative_type=simulation_type,
                                                                                              simulative_slots=si_slots,
                                                                                              simulative_assets=si_assets,
                                                                                              combine_type=selected_rank_col,
                                                                                              combine_weight=combine_weight,
                                                                                              combine_only=combine_checked,
                                                                                              combine_block=combine_block,
                                                                                              force_block_nums=force_block_nums,
                                                                                              enable_block_overheating=enable_block_overheating
                                                                                              )
        save_state(
            ['df_histogram', 'df_deal', 'df_hit', 'df_hístorical_deal', 'df_latest_hit', 'df_distribution', 'si_log'],
            [pd_histogram, ppd, pdp, pdx, pd_latest_hit, pd_plot, si_log])

        with st.spinner('⏳ Wait for rendering...'):
            excel_data = None
            if download_checked:
                excel_data = to_excel(pd_all)
                ymd = datetime.today().strftime('%Y-%m-%d')
                st.download_button(label='📥 Export to Excel file',
                                   data=excel_data,
                                   file_name=f'evaluate_{ymd}.xlsx',
                                   mime='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
            if si_log:
                stf.write('Simulation log')
                ymd = datetime.today().strftime('%Y-%m-%d_%H.%M.%S')
                st.download_button(label='📥 Download log',
                                   data=si_log,
                                   file_name=f'log_{selected_filter}_{ymd}.jsonl',
                                   mime='application/json')

        del ppd, pdp, pdx, pd_latest_hit, pd_all, excel_data

        logger.info("END **Evaluate and Search**")

    # if st.session_state.get("eval_done"):
    if st.session_state.get('df_deal', pd.DataFrame()).shape[0] > 0:
        stf.write('Pattern deal result')
        stf.dataframe(st.session_state['df_deal'], width=1500)

    if st.session_state.get('df_hit', pd.DataFrame()).shape[0] > 0:
        stf.write('Pattern hit result')
        stf.dataframe(st.session_state['df_hit'], width=1500)

    if st.session_state.get('df_hístorical_deal', pd.DataFrame()).shape[0] > 0:
        stf.write('Historical deal')
        gb = GridOptionsBuilder.from_dataframe(st.session_state['df_hístorical_deal'])
        gb.configure_pagination(paginationAutoPageSize=False, paginationPageSize=50)
        gb.configure_default_column(
            resizable=False,
            filter=True,
            groupable=False,
            sorteable=False,
            editable=False
        )
        grid_options = gb.build()

        AgGrid(
            st.session_state['df_hístorical_deal'],
            gridOptions=grid_options,
            height=400,
            theme="streamlit",
            fit_columns_on_grid_load=True,
            use_container_width=True,
            show_search=False,
            update_mode=GridUpdateMode.MODEL_CHANGED,
            reload_data=False,
            allow_unsafe_jscode=True,
        )

    if st.session_state.get('df_latest_hit', pd.DataFrame()).shape[0] > 0:
        stf.write('Latest hits')
        stf.dataframe(st.session_state['df_latest_hit'], height=200, width=1500)

    if st.session_state.get('df_distribution', pd.DataFrame()).shape[0] > 0:
        st.bar_chart(st.session_state['df_distribution'], x="quarter", width=1500, use_container_width=False)

    if st.session_state.get("si_log") is not None:
        stf.write('Log Visualizer')
        # if st.button('Trading Log Dashboard'):
        #     show_log_dashboard(st.session_state.get("si_log"))
        with st.expander("📊 Show Trading Log Dashboard", expanded=False):
            show_log_dashboard(st.session_state["si_log"])

    st.markdown('######')
    on = st.toggle("Enable debugging")

    if on:
        if "df_histogram" in st.session_state and isinstance(st.session_state["df_histogram"], dict) and len(
                st.session_state["df_histogram"]) > 0:
            selected_pattern = st.selectbox('Select pattern', st.session_state.df_histogram.keys())
            st.write(f"Displaying histogram for {selected_pattern}")
            data = st.session_state.df_histogram[selected_pattern]
            st.bar_chart(data, x="quarter", use_container_width=True)

        # User input for selecting stock file
        stock_file = st.selectbox("**Select a stock file:**", stock_files)
        st.write(f"Displaying data for {stock_file.split('.')[0]}")
        # Load data and plot visualization
        df = load_data(stock_file, dictFilter)
        df_aggrid, grid_option = edit_ticker_and_display(df, dictFilter)
        AgGrid(
            df_aggrid,
            gridOptions=grid_option,
            height=400,
            theme="streamlit",
            fit_columns_on_grid_load=False,
            use_container_width=True,
            show_search=False,

        )

        indicators = st.text_input("Enter the indicators you want to plot",
                                   placeholder="Ex: MA10, MA20, etc.")
        indicators = [ind.strip() for ind in indicators.split(",")]

        st.plotly_chart(plot_stock_data(df, dictFilter, indicators))

    return on


def st_evaluate_by_weight(stock_files, custom_filters):
    stf = st
    selected_filter = stf.selectbox("**Select a custom filter:**", list(custom_filters.keys()))

    custom_filter_text, filter_weight_text, filter_params, filter_combine = get_selected_filters(selected_filter,
                                                                                                 custom_filters)

    # Session State management
    # Check selected filter in session state
    if not st.session_state.get("current_filter") == selected_filter \
            or not st.session_state.get("current_eval_type") == "weight":
        st.session_state.current_eval_type = "weight"

        st.session_state.current_filter = selected_filter
        st.session_state.slider_cutloss = filter_params['cutloss']
        st.session_state.ratio_si_type = filter_params['si_type']
        st.session_state.num_si_slot = filter_params['si_slot']
        st.session_state.num_si_asset = filter_params['si_asset']
        st.session_state.num_lookback = filter_params['w_lookback']
        st.session_state.num_thres_buy = filter_params['w_thres_buy']
        st.session_state.num_thres_sell = filter_params['w_thres_sell']
        st.session_state.num_k_exp = filter_params['w_k_exp']
        st.session_state.selectbox_co_rank = filter_params['co_rank']

    col0, _ = stf.columns([1.5, 3])

    with col0:
        s_type = st.radio(
            "**Select an simulation type**",
            ["Full cash fix slot", "Full cash not fix slot", "Cash allocation"],
            key='ratio_si_type',
        )
        if s_type == 'Full cash fix slot':
            simulation_type = SIMULATE_FULL_CASH
        elif s_type == 'Full cash not fix slot':
            simulation_type = SIMULATE_FULL_CASH_NOT_FIX
        elif s_type == 'Cash allocation':
            simulation_type = SIMULATE_ALLOCATE

    def sdict2df(s, w):
        d = json.loads(s)
        d = collections.OrderedDict(sorted(d.items()))

        w = json.loads(w)
        weights_list = [w.get(key, 0) for key in d.keys()]

        pdx = pd.DataFrame({
            'weight': weights_list,
            'filter': list(d.keys()),
            'value': list(d.values())})

        pdx['weight'] = pdx['weight'].astype(float)
        pdx['filter'] = pdx['filter'].astype(str)
        pdx['value'] = pdx['value'].astype(str)
        return pdx

    def df2sdict(df):
        filter_dict = dict(zip(df['filter'], df['value']))
        weight_dict = dict(zip(df['filter'], df['weight']))

        return json.dumps(filter_dict), json.dumps(weight_dict)

    edited_df = stf.data_editor(sdict2df(custom_filter_text, filter_weight_text), hide_index=True, num_rows='dynamic',
                                use_container_width=True,
                                column_config={
                                    'weight': stf.column_config.NumberColumn("Weight", width='small'),
                                    'filter': stf.column_config.TextColumn("Filter", width='medium'),
                                    'value': stf.column_config.TextColumn("Value", width='large')
                                })
    custom_filter_text, filter_weight_text = df2sdict(edited_df)
    dictFilter = preprocess_dictFilter(custom_filter_text)
    dictWeight = preprocess_dictWeightFilter(filter_weight_text)

    # Check edited in session state
    if "df_value" not in st.session_state or not edited_df.equals(st.session_state["df_value"]):
        # This will only run if
        # 1. Some widget has been changed (including the dataframe editor), triggering a
        # script rerun, and
        # 2. The new dataframe value is different from the old value
        # Check logical error
        try:
            if "Init" not in dictFilter.keys():
                k = "Init"
                raise ValueError('Missing "Init" key in the filter')

            for k, v in dictFilter.items():
                check_parentheses_balance(v)
                check_logical_operators(v)
                check_comparison_operators(v)


        except ValueError as e:
            stf.error(f"Error: {k}: {e}")

        st.session_state["df_value"] = edited_df

    # Create a layout with two columns
    col1, col2, col0 = stf.columns(3)
    with col0:
        CUTLOSS = stf.slider("Cutloss %", 1, 100, key="slider_cutloss", value=filter_params.get('cutloss')) / 100.

        _, col_1, col_2, _ = stf.columns([0.1, 1, 1, 0.1])
        with col_1:
            si_slots = stf.number_input("Simulative slots", min_value=1, max_value=100,
                                        value=filter_params.get('si_slot'), step=1,
                                        key="num_si_slot")
        with col_2:
            si_assets = stf.number_input("Simulative assets (unit: 1 billion)", min_value=0.0, max_value=20000.0,
                                         value=filter_params.get('si_asset'), step=0.1, key="num_si_asset")
            si_assets *= 1e9

    with col2:
        _, col_3, col_4, _ = stf.columns([0.1, 1, 1, 0.1])
        with col_3:
            lookback = stf.number_input("Lookback", min_value=5, max_value=20, value=5, step=1, key="num_lookback")
        with col_4:
            k_exp = stf.number_input("K_exp", min_value=-0.05, max_value=1.0, value=0.0, step=0.05, key="num_k_exp")

        _, col_1, col_2, _ = stf.columns([0.1, 1, 1, 0.1])
        with col_1:
            thres_sell = stf.number_input("Sell threshold", min_value=-2.6, max_value=-0.1, value=-1.0, step=0.1,
                                          key="num_thres_sell")
        with col_2:
            thres_buy = stf.number_input("Buy threshold", min_value=0.1, max_value=2.6, value=1.0, step=0.1,
                                         key="num_thres_buy")

    # Save and overwrite the selected custom filter
    with col1:
        new_filter_name = stf.text_input("Enter a name if you want to save as a new profile",
                                         placeholder="Fill in here")

        broadcast = 0
        profile_col, profile_col1 = stf.columns(2)
        with profile_col:
            if stf.button("Save Profile"):
                # Create a new custom filter
                new_filter = parse_custom_filters(f=custom_filter_text,
                                                  weight=filter_weight_text,
                                                  combine=filter_combine,
                                                  params={
                                                      'cutloss': st.session_state.slider_cutloss,
                                                      'si_type': st.session_state.ratio_si_type,
                                                      'si_slot': st.session_state.num_si_slot,
                                                      'si_asset': st.session_state.num_si_asset,
                                                      "w_lookback": st.session_state.num_lookback,
                                                      "w_thres_buy": st.session_state.num_thres_buy,
                                                      "w_thres_sell": st.session_state.num_thres_sell,
                                                      "w_k_exp": st.session_state.num_k_exp,
                                                      "co_rank": filter_params['co_rank'],
                                                  })

                if new_filter_name and (new_filter_name not in custom_filters):
                    custom_filters[new_filter_name] = new_filter
                    broadcast = 1
                else:
                    # Overwrite the existing custom filter
                    custom_filters[selected_filter] = new_filter
                    broadcast = 2

                # Save the updated custom filters to config.json
                save_custom_filters(custom_filters)

        with profile_col1:
            @st.dialog("Do you want to delete this profile?")
            def vote(selected_filter):
                st.write(f"Are you sure you want to delete '{selected_filter}' profile?")
                if st.button("OK"):
                    del custom_filters[selected_filter]
                    save_custom_filters(custom_filters)
                    st.rerun()

            if stf.button("Delete Profile"):
                vote(selected_filter)

        if broadcast == 1:
            stf.success(f"Profile '{new_filter_name}' saved successfully!")
        elif broadcast == 2:
            stf.success(f"Profile '{selected_filter}' updated successfully!")

    st.markdown('######')
    if stf.button("**Evaluate and Search**"):

        logger.info("START **Evaluate and Search**")
        logger.info(f"{selected_filter}: {dictFilter}")
        logger.info(f"{selected_filter}: {dictWeight}")

        df_summary, df_keep, df_skip, si_log, df_dis, df_dis_q, df_dis_score = eval_filter_by_weight(
            dictFilter,
            dictWeight,
            lookback=lookback,
            k_exp=k_exp,
            thres_buy=thres_buy,
            thres_sell=thres_sell,
            CUTLOSS=CUTLOSS,
            simulation_type=simulation_type,
            simulative_slots=si_slots,
            simulative_assets=si_assets
        )
        df_all = pd.concat([df_keep, df_skip], ignore_index=True)

        with st.spinner('⏳ Wait for rendering...'):

            if df_summary.shape[0] > 0:
                stf.write('Summary result')
                stf.dataframe(df_summary, width=1500, hide_index=True)
            else:
                stf.write('Summary result: none')

            stf.write('Simulation log')
            ymd = datetime.today().strftime('%Y-%m-%d_%H.%M.%S')
            st.download_button(label='📥 Download log',
                               data=si_log,
                               file_name=f'log_{selected_filter}_{ymd}.txt',
                               mime='text/plain')

            tab_keep, tab_skip, tab_all = st.tabs(['Keep', 'Skip', 'All'])
            with tab_keep:
                if df_keep.shape[0] > 0:
                    stf.dataframe(df_keep, width=1500)
                else:
                    stf.write('Keep result: none')

            with tab_skip:
                if df_skip.shape[0] > 0:
                    stf.dataframe(df_skip, width=1500)
                else:
                    stf.write('Skip result: none')

            with tab_all:
                if df_summary.shape[0] > 0:
                    stf.dataframe(df_all, width=1500)
                else:
                    stf.write('All result: none')

            if df_dis.shape[0] > 0:
                stf.write('Hit distribute result')
                stf.dataframe(df_dis, width=1500, hide_index=True)
            else:
                stf.write('Hit distribute result: none')

            if df_dis_q.shape[0] > 0:
                stf.write('Hit distribution are deduplicated by quarter and score')
                stf.dataframe(df_dis_q, width=1500, hide_index=True)
            else:
                stf.write('Quarterly hit distribute result: none')

            if df_dis_score.shape[0] > 0:
                stf.write('Filter distribute are deduplicated by quarter and score')
                stf.dataframe(df_dis_score, width=1500, hide_index=True)
            else:
                stf.write('Filter distribute result: none')

            #
            # if pd_plot.shape[0] > 0:
            #     st.bar_chart(pd_plot, x="quarter", width=1500, use_container_width=False)

            # if st.session_state.get("df_plot"):
            #     selected_pattern = st.selectbox('Select pattern', st.session_state.df_plot.keys())
            #     st.write(f"Displaying histogram for {selected_pattern}")
            #     data = st.session_state.df_plot[selected_pattern]
            #     st.bar_chart(data, x="quarter", use_container_width=True)
        # del ppd, pdp, pdx, pdy, pd_all, excel_data
        logger.info("END **Evaluate and Search**")

    st.markdown('######')
    on = st.toggle("Enable debugging")

    if on:
        # User input for selecting stock file
        stock_file = st.selectbox("**Select a stock file:**", stock_files)
        # Load data and plot visualization
        st.write(f"Displaying data for {stock_file.split('.')[0]}")
        # Load data and plot visualization
        df = load_data(stock_file, dictFilter)
        df_aggrid, grid_option = edit_ticker_and_display(df, dictFilter)
        AgGrid(
            df_aggrid,
            gridOptions=grid_option,
            height=400,
            theme="streamlit",
            fit_columns_on_grid_load=False,
            use_container_width=True,
            show_search=False,

        )

        # indicators = st.text_input("Enter the indicators you want to plot",
        #                            placeholder="Ex: MA10, MA20, etc.")
        # indicators = [ind.strip() for ind in indicators.split(",")]

        # st.plotly_chart(plot_stock_data(df, dictFilter, indicators))


def st_evaluate_shortsell(stock_files, custom_filters):
    stf = st
    selected_filter = stf.selectbox("**Select a custom filter:**", list(custom_filters.keys()))

    custom_filter_text, filter_weight_text, filter_params, filter_combine = get_selected_filters(selected_filter,
                                                                                                 custom_filters)

    # Session State management
    st.session_state.eval_done = True
    # Check selected filter in session state
    if not st.session_state.get("current_filter") == selected_filter \
            or not st.session_state.get("current_eval_type") == "shortsell":
        st.session_state.current_eval_type = "shortsell"

        st.session_state.current_filter = selected_filter
        st.session_state.slider_cutloss = filter_params['cutloss']
        st.session_state.ratio_si_type = filter_params['si_type']
        st.session_state.num_si_slot = filter_params['si_slot']
        st.session_state.num_si_asset = filter_params['si_asset']
        st.session_state.num_lookback = filter_params['w_lookback']
        st.session_state.num_thres_buy = filter_params['w_thres_buy']
        st.session_state.num_thres_sell = filter_params['w_thres_sell']
        st.session_state.num_k_exp = filter_params['w_k_exp']
        st.session_state.selectbox_co_rank = filter_params['co_rank']

        # clear old eval data
        try:
            for key in ["df_plot", "df_deal", "df_hit"]:
                if key in st.session_state.keys():
                    del st.session_state[key]
        except ValueError as e:
            stf.error(f"Error: {e}")

    def sdict2df(s):
        d = json.loads(s)
        d = collections.OrderedDict(sorted(d.items()))
        pdx = pd.DataFrame({'filter': list(d.keys()), 'value': list(d.values())})
        pdx['filter'] = pdx['filter'].astype(str)
        pdx['value'] = pdx['value'].astype(str)
        return pdx

    def df2sdict(df):
        return json.dumps(dict(zip(df['filter'], df['value'])))

    edited_df = stf.data_editor(sdict2df(custom_filter_text), hide_index=True, num_rows='dynamic',
                                use_container_width=True)
    custom_filter_text = df2sdict(edited_df)
    dictFilter = preprocess_dictFilter(custom_filter_text)

    # Check edited in session state
    if "df_value" not in st.session_state or not edited_df.equals(st.session_state["df_value"]):
        # This will only run if
        # 1. Some widget has been changed (including the dataframe editor), triggering a
        # script rerun, and
        # 2. The new dataframe value is different from the old value
        # Check logical error
        try:
            if "Init" not in dictFilter.keys():
                k = "Init"
                raise ValueError('Missing "Init" key in the filter')

            for k, v in dictFilter.items():
                check_parentheses_balance(v)
                check_logical_operators(v)
                check_comparison_operators(v)

            # clear old eval data
            for key in ["df_plot", "df_deal", "df_hit"]:
                if key in st.session_state.keys():
                    del st.session_state[key]

        except ValueError as e:
            stf.error(f"Error: {k}: {e}")

        st.session_state["df_value"] = edited_df

    # Create a layout with two columns
    col1, col2, col0 = stf.columns(3)
    with col0:
        CUTLOSS = stf.slider("Cutloss %", 1, 100, key="slider_cutloss", value=filter_params.get('cutloss')) / 100.

    # Save and overwrite the selected custom filter
    with col1:
        new_filter_name = stf.text_input("Enter a name if you want to save as a new profile",
                                         placeholder="Fill in here")

        broadcast = 0
        profile_col, profile_col1 = stf.columns(2)
        with profile_col:
            if stf.button("Save Profile"):
                # Create a new custom filter
                new_filter = parse_custom_filters(f=custom_filter_text,
                                                  weight=filter_weight_text,
                                                  combine=filter_combine,
                                                  params={
                                                      'cutloss': st.session_state.slider_cutloss,
                                                      'si_type': filter_params['si_type'],
                                                      'si_slot': filter_params['si_slot'],
                                                      'si_asset': filter_params['si_asset'],
                                                      "w_lookback": filter_params['w_lookback'],
                                                      "w_thres_buy": filter_params['w_thres_buy'],
                                                      "w_thres_sell": filter_params['w_thres_sell'],
                                                      "w_k_exp": filter_params['w_k_exp'],
                                                      "co_rank": filter_params['co_rank'],
                                                  })
                if new_filter_name and (new_filter_name not in custom_filters):
                    custom_filters[new_filter_name] = new_filter
                    broadcast = 1
                else:
                    # Overwrite the existing custom filter
                    custom_filters[selected_filter] = new_filter
                    broadcast = 2

                # Save the updated custom filters to config.json
                save_custom_filters(custom_filters)

        with profile_col1:
            @st.dialog("Do you want to delete this profile?")
            def vote(selected_filter):
                st.write(f"Are you sure you want to delete '{selected_filter}' profile?")
                if st.button("OK"):
                    del custom_filters[selected_filter]
                    save_custom_filters(custom_filters)
                    st.rerun()

            if stf.button("Delete Profile"):
                vote(selected_filter)

        if broadcast == 1:
            stf.success(f"Profile '{new_filter_name}' saved successfully!")
        elif broadcast == 2:
            stf.success(f"Profile '{selected_filter}' updated successfully!")

    st.markdown('######')
    download_checked = st.checkbox('Download deals data', False)

    if stf.button("**Evaluate and Search**"):

        # with open('filter.json', 'w', encoding='utf-8') as f:
        #     json.dump(dictFilter, f, ensure_ascii=False, indent=4)
        # with open('cus_filter.json', 'w', encoding='utf-8') as f:
        #     json.dump(custom_filter_text, f, ensure_ascii=False, indent=4)

        logger.info("START **Evaluate and Search**")
        logger.info(f"{selected_filter}: {dictFilter}")

        pds, pd_sd, pd_short_all, pd_histogram = eval_filter_shortsell(dictFilter, CUTLOSS=CUTLOSS)
        save_state(['df_plot', 'df_deal', 'eval_done'], [pd_histogram, pds, False])

        with st.spinner('⏳ Wait for rendering...'):
            excel_data = None
            if download_checked:
                excel_data = to_excel(pd_short_all)
                ymd = datetime.today().strftime('%Y-%m-%d')
                st.download_button(label='📥 Export to Excel file',
                                   data=excel_data,
                                   file_name=f'evaluate_{ymd}.xlsx',
                                   mime='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
            if pds.shape[0] > 0:
                stf.write('pattern short sell deal result')
                stf.dataframe(pds, width=1500)

            if pd_sd.shape[0] > 0:
                stf.write('historical short sell deal')
                stf.dataframe(pd_sd, height=200, width=1500)

        del pds, pd_sd, excel_data

        logger.info("END **Evaluate and Search**")

    if st.session_state.get("eval_done"):
        if isinstance(st.session_state.get('df_deal'), pd.DataFrame) and not st.session_state.get('df_deal').empty:
            stf.write('Deal result in memory')
            stf.dataframe(st.session_state['df_deal'], width=1500)

        if isinstance(st.session_state.get('df_hit'), pd.DataFrame) and not st.session_state.get('df_hit').empty:
            stf.write('Hit result in memory')
            stf.dataframe(st.session_state['df_hit'], width=1500)

    st.markdown('######')
    on = st.toggle("Enable debugging")

    if on:
        if "df_plot" in st.session_state and isinstance(st.session_state["df_plot"], dict) and len(
                st.session_state["df_plot"]) > 0:
            selected_pattern = st.selectbox('Select pattern', st.session_state.df_plot.keys())
            st.write(f"Displaying histogram for {selected_pattern}")
            data = st.session_state.df_plot[selected_pattern]
            st.bar_chart(data, x="quarter", use_container_width=True)

        # User input for selecting stock file
        stock_file = st.selectbox("**Select a stock file:**", stock_files)
        # Load data and plot visualization
        st.write(f"Displaying data for {stock_file.split('.')[0]}")
        # Load data and plot visualization
        df = load_data(stock_file, dictFilter)
        df_aggrid, grid_option = edit_ticker_and_display(df, dictFilter)
        AgGrid(
            df_aggrid,
            gridOptions=grid_option,
            height=400,
            theme="streamlit",
            fit_columns_on_grid_load=False,
            use_container_width=True,
            show_search=False,

        )

        indicators = st.text_input("Enter the indicators you want to plot",
                                   placeholder="Ex: MA10, MA20, etc.")
        indicators = [ind.strip() for ind in indicators.split(",")]

        st.plotly_chart(plot_stock_data(df, dictFilter, indicators))


if __name__ == "__main__":
    import sys

    # current_dir = os.path.dirname(os.path.abspath(__file__))
    # current_dir = current_dir.replace("/webui", "")
    # os.chdir(current_dir)
    # sys.path.insert(0, current_dir)
    #
    # PROFILE = "selected_selected_init_50b_25slot_cs_v2"
    # configFilters = json.loads(open("config.json", "r").read())
    # dictFilter = json.loads(configFilters[PROFILE]['filter'])
    # if 'Init' in dictFilter.keys():
    #     for key, value in dictFilter.items():
    #         dictFilter[key] = value.replace("{Init}", dictFilter['Init'])
    #
    # com_weight = json.loads(configFilters[PROFILE]['combine']['synthetic'])
    #
    # weights = {
    #     "BKMA200": 0.148,
    #     "TrendingGrowth": 0.74,
    #     "TL3M": 1.45,
    #     "RSILow30": 0.65,
    #     "UnderBV": 0.71,
    #     "SuperGrowth": 0.9,
    #     "SurpriseEarning": 0.25,
    #     "Conservative": 0.7,
    #
    #     "Hold": -1,
    #     "MA21": -0.41,
    #     "MA31": -0.72,
    #     "MA41": -0.26,
    #     "S13": -1.7,
    #     "SellLowGrowth": -1.43,
    #     "SellResistance3M": -1.16,
    #     "SellResistance1M": -0.69,
    #     "SellResistance": -1,
    #     "SellBV": -0.33,
    #     "SellBV2": -1.22,
    #     "SellPE": -1.43,
    #     "SellVolMax": -0.58,
    #     "BearDvg2": -1.518,
    # }
    # eval_filter_all(dictFilter=dictFilter, simulative_type=SIMULATE_ALLOCATE, simulative_slots=2, combine_weight=com_weight)
    # eval_filter_by_weight(dictFilter, weights, thres_buy=1, thres_sell=-1, CUTLOSS=0.15)
    # eval_filter_shortsell(dictFilter)
    main()
