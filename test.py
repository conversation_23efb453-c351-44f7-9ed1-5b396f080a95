# # external_script.py
import re

from celery import Celery
import datetime
import os
from core_utils.gcstorage import GoogleCloudStorageService
from core_utils.stockquery import StockQuery
import pandas as pd

#
# Initialize the service with your bucket name

from io import BytesIO
from explo import tdlib as td
# Broker and Backend point to the Docker host IP (assume localhost)
# app = Celery(
#     'b_jobs',
#     broker='redis://localhost:6379/0',  # Replace 'localhost' with the Docker host IP
#     backend='redis://localhost:6379/0'
# )
app = Celery(
    'b_jobs',
    broker='redis://*************:6379/0',  # Replace 'localhost' with the Docker host IP
    backend='redis://*************:6379/0'
)
bucket_name = "tav2-gs"  # Replace with your bucket name
# bucket_name = "kaffav2"  # Replace with your bucket name
os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = "core_utils/env/gcskey.json"
gcs_service = GoogleCloudStorageService(bucket_name)
stock_query = StockQuery(end_date='2026-01-01')
ticker = "HPG"
filter = {
        "_BKMA200": "((Volume*Price>1e+9) & (time>='2017-01-01') & (time<='2025-01-01')) & ((ID_LO_3Y-ID_HI_3Y)>480) & (MA50/MA200>1.1) & (MA10/MA200<1.2) & (ROE5Y >0.1) & (PE <20) & (NP_P0 > NP_P1) ",
        "_BKVOL5Y": "((Volume*Price>1e+9) & (time>='2017-01-01') & (time<='2025-01-01')) & (Volume >= Volume_Max5Y) & (ROE5Y>0.05)  & (NP_P0 > NP_P1*1.2) & (PE<20) & (FSCORE>=5)",
        # "_T2P5X": "((Volume*Price>1e+9) & (time>='2017-01-01') & (time<='2025-01-01')) & (C_H2Y<0.7) & (C_H3M<0.8)  & (W_CMB_Step>0)  &  (W_CMB_LEN>=5)  &  (W_CMB_LAG>0) &  (W_CMB_LAG<=4)  &  (M_CMB_Step>0)  &  (M_CMB_LEN>=3)  &  (M_CMB_LAG >0)  &  (M_CMB_LAG <=4)",
        # "_T3P4": "((Volume*Price>1e+9) & (time>='2017-01-01') & (time<='2025-01-01')) & ((((W_CMB_Step>0) & (W_CMB_LEN>=5) & (W_CMB_LAG>0)& (W_CMB_LAG<=3)) | ((M_CMB_Step>0) & (M_CMB_LEN>=3) & (M_CMB_LAG >=1) & (M_CMB_LAG <=4)))) & (ROE5Y>=0.1)&(ROE5Y<=0.2)&(NP_R>0)&(PE<10)&(C_H2Y<0.7) & (C_H2Y>0.5)",
        # "_T3P6": "((Volume*Price>1e+9) & (time>='2017-01-01') & (time<='2025-01-01')) & ((((W_CMB_Step>0) & (W_CMB_LEN>=5) & (W_CMB_LAG>0)& (W_CMB_LAG<=3)) | ((M_CMB_Step>0) & (M_CMB_LEN>=3) & (M_CMB_LAG >=1) & (M_CMB_LAG <=4)))) & (ROE5Y>=0.1)&(ROE5Y<=0.2)&(NP_P0<0)&(PB<0.8)&(C_H2Y<0.6)&(C_H2Y>0.2)",
        # "_TL3M": "((Volume*Price>1e+9) & (time>='2017-01-01') & (time<='2025-01-01')) & (HI_3M_T1/LO_3M_T1<1.3) & (Volume > Volume_3M_P90)& (ROE5Y>0.1) & (PE<20) & (PB < 1.5) & (FSCORE > 4) & (NP_P0 > 1.1*NP_P1) & (PCF>0) & (NP_P0 > 0) & (PE >0)",
        # "_VolMax5Y": "(Close>Volume_Max5Y_High) & (ROE5Y>0.1)&(PE<20)& (NP_P0 > 1.2*NP_P1) & (PE >0) & (FSCORE >5) & (PCF>0)& (Price*Volume>1000e+6) ",
        "~MA2": "(PE>15) & (MA20/MA50<1) & (MA20_T1/MA50_T1>1)",
        "~MA3": "(PE<15) & (Close/MA200<1) & (Close_T1/MA200_T1>1)",
        "~MA4": "(Close > 1.5*MA200) & (NP_P0 < 0.8*NP_P1)",
        # "~S13": "(TrueC_L1W>=1.2) & (D_CMB_Peak_T1>D_CMB) & (Close>Close_T1) & (D_CMB_XFast<2)",
        # "~SellMaxVol": " (Close < Volume_Max5Y_Low)",
        # "~SellTest": "(Close > 2*BVPS) & (NP_P0 < NP_P1) &(Close < VAP1M)"
    }
# pd_deal = pd.read_csv('pd_deal.csv')
# start_date = '2017-01-01'
# end_date = '2026-01-01'
# print(app.conf.beat_schedule)
# print(app.tasks.keys())
# tasks = list(sorted(name for name in app.tasks
#                             if not name.startswith('celery.')))
# print(tasks)
# print(app.autodiscover_tasks(['celery_app.task']))
# Trigger a task
# for i in range(10):
now = datetime.datetime.now()
# app.send_task('tasks.schedule_tasks.pineline', args=[False])
# app.send_task('tasks.data_tasks.update_index', args=['VNINDEX', now, False])
# app.send_task('tasks.data_tasks.update_enrich_index', args=['VNINDEX', now, False, True])
# app.send_task('tasks.data_tasks.update_stock_price', args=['VNINDEX', now, False])
# app.send_task('tasks.data_tasks.update_stock_adjust_price', args=['VNINDEX', now, False])


# app.send_task('tasks.data_tasks.update_financial_report', kwargs={'ticker': 'STB', 'now_time': now, 'daily_update': True, 'overwrite': False})
# app.send_task('tasks.handle_raw_data.update_stock_list', args=[False, False])


# app.send_task('tasks.data_tasks.process_index_indicator', args=[now])
# for i in ['FPT', 'FOC', 'APH', 'ANV', 'BVB', 'ANT', 'DGC', 'DHG', 'DPM', 'CDC', 'ACB', 'AIG', 'BRC', 'ACV', 'FMC',
#               'DPR']:
# app.send_task('tasks.data_tasks.process_stock_indicator', args=['VNINDEX', now, False])
# app.send_task('tasks.data_tasks.process_stock_indicator_v1', args=['VCA', now, True])
#
# app.send_task('tasks.schedule_tasks.update_financial_reports', args=[True, False])
# app.send_task('tasks.schedule_tasks.update_indexes', args=[True])
# app.send_task('tasks.schedule_tasks.update_data', args=[False])
# app.send_task('tasks.schedule_tasks.compute_indicators', args=[False])
# app.send_task('tasks.schedule_tasks.update_risk_indicators')
# df_futures = app.send_task('tasks.data_tasks.process_stock_with_range',
#                                     kwargs={"ticker": 'VNINDEX', "start_range": -10, "end_range": 0, "step": 1,
#                                             "amount_data": 1})
# df_futures = pd.DataFrame(df_futures.get())
# df_futures.to_csv('df_future.csv', index=False)
df_futures = pd.read_csv('df_future.csv')


def analysis_pattern(df_index_tail):
    index_pattern = {
        "~BearDvgVNI1": "(D_RSI_Max1W/D_RSI > 1.044)  & (D_RSI_Max3M > 0.74) & (D_RSI_Max1W < 0.72) & (D_RSI_Max1W>0.61) & (D_RSI_Max1W_Close/D_RSI_Max3M_Close > 1.028) & (D_RSI_Max3M_MACD/D_RSI_Max1W_MACD>1.11) & (D_MACDdiff < 0)  & ( Close/D_RSI_Max3M_Close > 0.96) & (D_RSI_MinT3 > 0.43) & (D_CMF < 0.13)",
        "~BearDvgVNI2": "(D_RSI_Max1W/D_RSI > 1.016)  & (D_RSI_Max3M > 0.77) & (D_RSI_Max1W < 0.79) & (D_RSI_Max1W>0.6) & (D_RSI_Max1W_Close/D_RSI_Max3M_Close > 1.008) & (D_RSI_Max3M_MACD/D_RSI_Max1W_MACD>1.1) & (D_MACDdiff < 0)  & ( Close/D_RSI_Max3M_Close > 0.97) & (D_RSI_MinT3 > 0.5) & (D_CMF < 0.15)"
    }
    results = []
    for k, v in index_pattern.items():
        count_invalid = 0
        str_result = f'{k}: Not match\n'
        list_query = [q.strip(" ()") for q in v.split('&')]
        for q in list_query:
            is_valid = df_index_tail.query(q)
            if not is_valid.empty:
                continue

            str_result += f'    Condition: {q} | Value: '
            for m_q in re.findall(r"[A-Za-z_]\w*", q):
                str_result += f'{m_q}: {df_index_tail[m_q].values[0]:.2f}, '
            str_result += '\n'
            count_invalid += 1

        if 0 < count_invalid <= 3:
            results.append(str_result)

        if count_invalid == 0:
            results.append(f'{k}: Hit pattern\n')

    return results
for i in range(df_futures.shape[0]):
    row = df_futures.iloc[i:i+1]
    print(f"Change: {(row['Change'].values[0])} - {analysis_pattern(row)}")

# filter = {
#     "~BearDvgVNI1": "(time>='2014-01-01') & (time<='2026-01-01') & (ticker=='VNINDEX')",
#     "~BearDvgVNI2": "(time>='2014-01-01') & (time<='2026-01-01') & (ticker=='VNINDEX') & (D_RSI_Max1W/D_RSI > 1.016)  & (D_RSI_Max3M > 0.77) & (D_RSI_Max1W < 0.79) & (D_RSI_Max1W>0.6) & (D_RSI_Max1W_Close/D_RSI_Max3M_Close > 1.008) & (D_RSI_Max3M_MACD/D_RSI_Max1W_MACD>1.1) & (D_MACDdiff < 0)  & ( Close/D_RSI_Max3M_Close > 0.97) & (D_RSI_MinT3 > 0.5) & (D_CMF < 0.15)"
# }
# results = []
# for f in filter:
#     try:
#         pdx = df_futures.query(f'({filter[f]})').sort_values('time', ascending=False).copy()
#         pdx['filter'] = f
#         results.append(pdx)
#     except:
#         continue
# pass
# app.send_task('tasks.schedule_tasks.pineline', args=[False])
# app.send_task('tasks.schedule_tasks.update_and_compute', args=[False])

# app.send_task('tasks.eval_tasks.Simulation', args=["dss", start_date, end_date, 1])
# app.send_task('tasks.eval_tasks.run_simulate', args=["dss", filter])
# a = app.send_task('tasks.eval_tasks.eval_filter_ticker', args=["HPG", filter])
# app.send_task('tasks.handle_raw_data.update_stock_list', kargs={'update_skip': False, 'update_monitor': True})
# update_stock_list.apply_async(kargs={'update_skip': False, 'update_monitor': True})

pass
# result = a.get()
# pass
# app.send_task('tasks.data_tasks.update_stock_list')
#
#
#
# # Get the result (you can also use .get() to wait for the result)
# print(result.status)
from datetime import datetime
import os
from core_utils.gcstorage import GoogleCloudStorageService
from core_utils.stockquery import StockQuery
import pandas as pd

#
# Initialize the service with your bucket name

from io import BytesIO
from explo import tdlib as td


def quarter_report(time):
    year, month, day = time.split('-')
    time = int(month) * 100 + int(day)
    if 100 < time <= 400:  # Jan 1st - April 0th
        # key = f'{int(year) - 1}Q4'
        year = int(year) - 1
        length = 4
    elif 400 < time <= 700:  # April 1st - July 0th
        # key = f"{year}Q1"
        year = int(year)
        length = 1
    elif 700 < time <= 1000:  # July 1st -October 0th
        # key = f"{year}Q2"
        year = int(year)
        length = 2
    else:  # October 1st - End year
        # key = f"{year}Q3"
        year = int(year)
        length = 3
    return year, length


def report_date(year, length):
    if length == '1' or length == 1:
        date = f"{year}-05-01"
    elif length == '2' or length == 2:
        date = f"{year}-08-01"
    elif length == '3' or length == 3:
        date = f"{year}-11-01"
    else:
        date = f"{int(year) + 1}-02-01"

    return date
    # return pd.to_datetime(date).date()




DICT_SHIFT = {'1W': 5, '2W': 10, '1M': 20, '3M': 60, '6M': 120, '1Y': 240, '2Y': 480}



def update_index_pe(ticker, now_time, daily_update=True, overwrite=False):
    df = stock_query.get_enrich_vnindex()

    if daily_update:
        now = now_time.strftime("%Y-%m")
        df = df.query(f'time.str.startswith("{now}")').reset_index(drop=True)
        if not df.empty:
            destination = f"rawdata/index_pe/ymd={now.replace('-', '')}/{ticker}.csv"
            gcs_service.upload_from_memory(df, destination)


    else:
        yms = df['time'].transform(lambda x: x[:7]).unique()
        file_list = [obj for obj in gcs_service.list_files(f"rawdata/index_pe/") if ticker in obj]
        for ym in yms:
            destination = f"rawdata/index_pe/ymd={ym.replace('-', '')}/{ticker}.csv"
            if overwrite or (destination not in file_list):
                df_ym = df.query(f'time.str.startswith("{ym}")').reset_index(drop=True).copy()
                gcs_service.upload_from_memory(df_ym, destination)


# ticker_df = gcs_service.download_file_to_memory("rawdata/stock_meta/latest/ticker_list.csv")
# ticker_df = pd.read_csv(BytesIO(ticker_df))
# list_ticker = [ticker['ticker'].value for ticker in ticker_df if not ticker['is_skip']]
# ticker_df = ticker_df[ticker_df['ticker'] == ticker]
# ticker_list = gcs_service.download_file_to_memory("rawdata/stock_meta/latest/ticker_lisdsdst.csv")
# update_index_pe('VNINDEX', datetime.now(), daily_update=False, overwrite=False)
# process_index_indicator(now)
# process_stock_indicator("VNINDEX", now, daily_update=False)
# preprocess_index("VNINDEX")
# update_index('VNINDEX', now, daily_update=True)
# update_stock_adjust_price('HPG', now, daily_update=True)
# process_index_indicator(now)
# process_stock_indicator("HPG", now, daily_update=True)
# Call preprocess
# update_financial_report("MWG", now, daily_update=False)
# preprocess_index("VNINDEX")
# update_stock_history("HPG")
# update_financial_report("HPG")
# a = gcs_service.list_files(prefix="rawdata/financial_report/")
# a = gcs_service.download_file_to_memory("rawdata/financial_report/upto_quarter=2023Q4/HPG.csv")
# from io import BytesIO
# import pandas as pd
#
# df = pd.read_csv(BytesIO(a))
# pass
# from vnstock3 import Vnstock
# stock_vci = Vnstock(show_log=False).stock(source='VCI')
#
# import pandas as pd
# import os
# FPATH = 'ticker_v1a/'
# list_processed_ticker = [f.replace('.csv', '') for f in os.listdir(FPATH) if f.endswith('.csv')]
#
# list_filter = []
# data = stock_vci.listing.symbols_by_exchange()
# data1 = stock_vci.listing.symbols_by_industries()
# for i, d in data1.iterrows():
#     try:
#         if d['icb_code4'] == '2353':
#             list_filter.append(data.query(f'symbol=="{d["symbol"]}"'))
#     except Exception as e:
#         continue
#
# list_filter = pd.concat(list_filter)
# list_filter.to_csv('list_company.csv', index=False)
