# -*- coding: utf-8 -*-
"""
Map Book1.csv (A) <-> b2.csv (B)
- Ưu tiên lấy từ B; nếu B thiếu -> lấy A
- Tự đoán/chuẩn hóa đơn vị: %, ratio, VND, Bn. VND, days
- Với chỉ tiêu không có trực tiếp ở B, tính "tương đương" từ các cột liên quan của B
- Xuất: merged_mapped.csv + merged_log.csv (có thể đổi đường dẫn cuối file)
"""

import pandas as pd
from typing import Optional, Dict, Any

# ---------- Helpers: unit parsing & conversion ----------

def parse_unit_from_name(col_name: str) -> Optional[str]:
    """
    Suy đoán đơn vị từ tên cột (B: vì hay có '(Bn. VND)', '(VND)', '(%)', 'Days ...').
    """
    if not isinstance(col_name, str):
        return None
    name = col_name.lower()
    if "(bn. vnd)" in name or "bn. vnd" in name or "bn vnd" in name:
        return "vnd_billion"
    if "(vnd)" in name:
        return "vnd"
    if "(%)" in name or "margin (%)" in name or "yoy (%)" in name:
        return "percent"
    if "days" in name:
        return "days"
    return None  # dimensionless/unknown

def guess_ratio_unit_from_values(series: pd.Series) -> str:
    """
    Đoán 1 cột tỷ lệ đang ở dạng ratio (0..1) hay percent (0..100).
    """
    s = pd.to_numeric(series, errors="coerce").dropna()
    if s.empty:
        return "unknown"
    # nếu đa số |x| <= ~1.2 => ratio
    return "ratio" if (s.abs() <= 1.2).mean() > 0.7 else "percent"

def normalize_value(value, src_unit: Optional[str], dst_unit: Optional[str]):
    """
    Convert đơn vị src -> dst. Giữ nguyên nếu không convert được.
    Hỗ trợ:
      - vnd_billion <-> vnd
      - ratio <-> percent
      - days/None: giữ nguyên
    """
    if value is None or pd.isna(value):
        return value
    try:
        x = float(value)
    except Exception:
        return value

    if src_unit == dst_unit or dst_unit is None or src_unit is None:
        return x

    # Tiền tệ
    if src_unit == "vnd_billion" and dst_unit == "vnd":
        return x * 1_000_000_000
    if src_unit == "vnd" and dst_unit == "vnd_billion":
        return x / 1_000_000_000

    # Tỷ lệ
    if src_unit == "ratio" and dst_unit == "percent":
        return x * 100.0
    if src_unit == "percent" and dst_unit == "ratio":
        return x / 100.0

    return x  # đơn vị khác không map -> giữ nguyên

def get_series(df: pd.DataFrame, col: Optional[str]) -> Optional[pd.Series]:
    return df[col] if (isinstance(col, str) and col in df.columns) else None

# ---------- Load dữ liệu ----------
A = pd.read_csv("/mnt/data/Book1.csv")   # file A
B = pd.read_csv("/mnt/data/b2.csv")      # file B

# ---------- Spec mapping (chuẩn hóa cột đích + đơn vị) ----------
# cột_đích -> {B: 'col_B', A: 'col_A', unit: 'đơn vị đích', ...derive...}
MAP: Dict[str, Dict[str, Any]] = {
    "period": {"B": "yearReport", "A": "periods", "unit": None},

    "price_to_earning": {"B": "P/E", "A": "price_to_earning", "unit": "dimensionless"},
    "price_to_book": {"B": "P/B", "A": "price_to_book", "unit": "dimensionless"},
    "ev_over_ebitda": {"B": "EV/EBITDA", "A": "value_before_ebitda", "unit": "dimensionless"},

    "roe_pct": {"B": "ROE (%)", "A": "roe", "unit": "percent"},
    "roa_pct": {"B": "ROA (%)", "A": "roa", "unit": "percent"},
    "ebit_margin_pct": {"B": "EBIT Margin (%)", "A": "ebit_on_revenue", "unit": "percent"},

    "days_sales_outstanding": {"B": "Days Sales Outstanding", "A": "days_receivable", "unit": "days"},
    "days_inventory_outstanding": {"B": "Days Inventory Outstanding", "A": "days_inventory", "unit": "days"},
    "days_payable_outstanding": {"B": "Days Payable Outstanding", "A": "days_payable", "unit": "days"},

    "interest_coverage": {"B": "Interest Coverage", "A": "ebit_on_interest", "unit": "dimensionless"},
    "current_ratio": {"B": "Current Ratio", "A": "current_payment", "unit": "dimensionless"},
    "quick_ratio": {"B": "Quick Ratio", "A": "quick_payment", "unit": "dimensionless"},
    "cash_ratio": {"B": "Cash Ratio", "A": "cash_circulation", "unit": "dimensionless"},

    "eps_vnd": {"B": "EPS (VND)", "A": "earning_per_share", "unit": "vnd"},
    "bvps_vnd": {"B": "BVPS (VND)", "A": "book_value_per_share", "unit": "vnd"},

    "asset_turnover": {"B": "Asset Turnover", "A": "revenue_on_asset", "unit": "dimensionless"},
    "debt_to_equity": {"B": "Debt/Equity", "A": "payable_on_equity", "unit": "dimensionless"},

    "sales_vnd_bn": {"B": "Sales", "A": "from_sale", "unit": "vnd_billion"},
    "financial_income_vnd_bn": {"B": "Financial Income", "A": "from_financial", "unit": "vnd_billion"},
    "investing_result_vnd_bn": {"B": "Profit/Loss from investing activities", "A": "from_invest", "unit": "vnd_billion"},
    "capex_purchase_fixed_assets_vnd_bn": {"B": "Purchase of fixed assets", "A": "capex_on_fixed_asset", "unit": "vnd_billion"},

    # Derived từ B nếu không có trực tiếp
    "equity_over_total_assets": {
        "B": None, "A": "equity_on_total_asset", "unit": "dimensionless",
        "derive": ("OWNER'S EQUITY(Bn.VND)", "TOTAL ASSETS (Bn. VND)")
    },
    "revenue_over_working_capital": {
        "B": None, "A": "revenue_on_work_capital", "unit": "dimensionless",
        "derive3": ("Revenue (Bn. VND)", "CURRENT ASSETS (Bn. VND)", "CURRENT LIABILITIES (Bn. VND)")
    },
    "post_tax_over_pre_tax": {
        "B": None, "A": "post_tax_on_pre_tax", "unit": "dimensionless",
        "derive": ("Net Profit For the Year", "Profit before tax")
    },
    "pre_tax_over_ebit": {
        "B": None, "A": "pre_tax_on_ebit", "unit": "dimensionless",
        "derive": ("Profit before tax", "EBIT (Bn. VND)")
    },
    "ebitda_over_change_inventory": {
        "B": None, "A": "ebitda_on_stock_change", "unit": "dimensionless",
        "derive": ("EBITDA (Bn. VND)", "Increase/Decrease in inventories")
    },
    "delta_bvps_vnd": {
        "B": None, "A": "book_value_per_share_change", "unit": "vnd",
        "derive_diff": "BVPS (VND)"
    },
    "free_cash_flow_vnd_bn": {
        "B": None, "A": "free_cash_flow", "unit": "vnd_billion",
        "derive_fcf": ("Net cash inflows/outflows from operating activities", "Purchase of fixed assets")
    },
}

# ---------- Build output ----------
n = max(len(A), len(B))
out = pd.DataFrame(index=range(n))
logs = []

for target, spec in MAP.items():
    desired_unit = spec.get("unit")
    colB = spec.get("B")
    colA = spec.get("A")
    sB = get_series(B, colB)
    sA = get_series(A, colA)

    used = None
    src = None
    src_unit = None
    col_from = None

    # 1) Lấy trực tiếp từ B nếu có
    if sB is not None:
        uB = parse_unit_from_name(colB)
        if desired_unit in ("percent", "ratio") and uB is None:
            # đoán nếu là tỷ lệ
            guess = guess_ratio_unit_from_values(sB)
            uB = guess if guess in ("percent", "ratio") else None
        src_unit = uB
        used = sB
        src = "B"
        col_from = colB

    # 2) Nếu B không có -> lấy A
    elif sA is not None:
        if desired_unit in ("percent", "ratio"):
            uA = guess_ratio_unit_from_values(sA)
        else:
            uA = None
        src_unit = uA
        used = sA
        src = "A"
        col_from = colA

    # 3) Nếu vẫn chưa có -> thử derive từ B
    if used is None and "derive" in spec:
        num_col, den_col = spec["derive"]
        s_num, s_den = get_series(B, num_col), get_series(B, den_col)
        if s_num is not None and s_den is not None:
            x = pd.to_numeric(s_num, errors="coerce")
            y = pd.to_numeric(s_den, errors="coerce")
            used = x / y
            src_unit = "dimensionless"
            src = "B(derived)"
            col_from = f"{num_col}/{den_col}"

    if used is None and "derive3" in spec:
        rev_col, ca_col, cl_col = spec["derive3"]
        s_rev, s_ca, s_cl = get_series(B, rev_col), get_series(B, ca_col), get_series(B, cl_col)
        if s_rev is not None and s_ca is not None and s_cl is not None:
            rev = pd.to_numeric(s_rev, errors="coerce")
            wc = pd.to_numeric(s_ca, errors="coerce") - pd.to_numeric(s_cl, errors="coerce")
            used = rev / wc
            src_unit = "dimensionless"
            src = "B(derived)"
            col_from = f"{rev_col}/({ca_col}-{cl_col})"

    if used is None and "derive_diff" in spec:
        base_col = spec["derive_diff"]
        s_base = get_series(B, base_col)
        if s_base is not None:
            used = pd.to_numeric(s_base, errors="coerce").diff()
            src_unit = parse_unit_from_name(base_col) or "vnd"
            src = "B(derived)"
            col_from = f"Δ {base_col}"

    if used is None and "derive_fcf" in spec:
        ocf_col, ppe_col = spec["derive_fcf"]
        s_ocf, s_ppe = get_series(B, ocf_col), get_series(B, ppe_col)
        if s_ocf is not None and s_ppe is not None:
            ocf = pd.to_numeric(s_ocf, errors="coerce")
            capex = pd.to_numeric(s_ppe, errors="coerce")  # thường âm (chi ra), lấy trị tuyệt đối
            used = ocf - capex.abs()
            src_unit = "vnd_billion"
            src = "B(derived)"
            col_from = f"{ocf_col} - |{ppe_col}|"

    # 4) Chuẩn hóa đơn vị -> desired_unit
    if used is not None:
        # đoán tỷ lệ nếu cần
        if desired_unit in ("percent", "ratio") and src_unit is None:
            src_unit = guess_ratio_unit_from_values(used)
            if src_unit == "unknown":
                src_unit = "ratio"  # mặc định
        out[target] = used.apply(lambda v: normalize_value(v, src_unit, desired_unit))
        logs.append({
            "target": target,
            "source": src,
            "col_from": col_from,
            "desired_unit": desired_unit,
            "src_unit": src_unit,
            "note": ""
        })
    else:
        out[target] = None
        logs.append({
            "target": target,
            "source": "None",
            "col_from": None,
            "desired_unit": desired_unit,
            "src_unit": None,
            "note": "Không tìm thấy ở B và A"
        })

# ---------- Xuất kết quả ----------
out.to_csv("/mnt/data/merged_mapped.csv", index=False)
pd.DataFrame(logs).to_csv("/mnt/data/merged_log.csv", index=False)

print("DONE -> /mnt/data/merged_mapped.csv")
print("LOG  -> /mnt/data/merged_log.csv")
