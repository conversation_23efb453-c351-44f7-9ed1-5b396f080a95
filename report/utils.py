import json
import os
import re
import uuid
from datetime import datetime
import json
import fitz
import pandas as pd
import pyppeteer
from sympy import symbols, Eq, solve, sympify, Rel, solve_univariate_inequality, Interval
from core_utils.dictionary import DICTIONARY
from pathos.multiprocessing import ProcessingPool as Pool

async def html2pdf(html_path):
    path = html_path.split(".")[0] + ".pdf"
    abs_path = os.path.abspath(html_path)
    # browser = await launch()
    # browser = await pyppeteer.launch(
    #     headless=True,
    #     executablePath='/usr/bin/chromium-browser',
    #     args=['--no-sandbox', '--disable-setuid-sandbox']
    #     # args=["--no-sandbox"]
    # )
    browser = await pyppeteer.connect({
        "browserWSEndpoint": 'ws://*************:3000',
    })
    page = await browser.newPage()

    with open(abs_path, "r", encoding="utf-8") as f:
        html_content = f.read()
    await page.setContent(html_content)
    await page.waitForFunction('document.readyState === "complete"')
    # await page.goto(f'file://{abs_path}', {
    #     "waitUntil": 'networkidle2'
    # })
    await page.pdf(
        path=path,
        width="15in",
        height="25in",
        margin={
            "top": 5,
            "right": 5,
            "bottom": 5,
            "left": 5,

        },
        printBackground=True,
        # format='A4',
    )
    await browser.close()

    return path


def last_months(y, m):
    if m != 1:
        m = m - 1
    else:
        m = 12
        y = y - 1
    return f'{y}-{m:02d}'


def last_weeks(y, w):
    m_list = [f'{y}-{w:02d}']
    if w != 1:
        m = w - 1
    else:
        m = 48
        y = y - 1
    m_list.append(f'{y}-{m:02d}')
    return m_list


def sort_by_key(df):
    filter = ['VolMax', 'BKMA200', 'TL3M', 'TL3Mv1', 'T3P4', 'T3P6V1', 'T3P5', 'PKDW8', 'T3P6', 'T2P5X', 'T2P5', 'PKDM']
    si_return = []
    data = pd.DataFrame({'filter': filter, 'si_return': si_return})
    df = df.merge(data, on='filter', how='left')
    return df.sort_values(['si_return', 'filter', "time", "ticker"], ascending=[False, True, False, True]).reset_index(
        drop=True)


def load_data(fname, fpath="/"):
    df = pd.read_csv(fpath + fname.replace('.csv', '').replace('*', '') + '.csv', dtype={'time': str, 'ticker': str}) \
        .query('time>="2023"').reset_index(drop=True)
    df['ymd'] = pd.to_datetime(df['time'])
    # print(df.head())
    return df


def apply_filter(df, filter):
    idx = df.query(filter).index
    return df.index.isin(idx).astype(int)


def src_html_img(fig, FPATH="assets"):
    img_name = uuid.uuid4().hex[:6]
    path = os.path.join(FPATH, "{}.png".format(img_name))
    img_src = f"{img_name}.png"
    fig.write_image(path)
    html_block = f'<img src="{img_src}" alt="Image" style="width:1200px;height:300px;">'
    # html_block = dp.HTML(html_block)
    # html_block = f'<img src="{path}" alt="Image">'

    return html_block


def merge_pdfs(paths, output):
    """
    Merge pdfs
    :param paths:
    :param output:
    :return:
    """
    pdf_merger = fitz.open()
    for path in paths:
        pdf_document = fitz.open(path)
        pdf_merger.insert_pdf(pdf_document)

    pdf_merger.save(output)
    pdf_merger.close()


indicator_functions = {}


def get_indicator(indicator_name, data_frame):
    if isinstance(indicator_functions[indicator_name], str):
        return eval(indicator_functions[indicator_name])

    return indicator_functions[indicator_name](data_frame)


#############################

def BKMA200(pd_all: pd.DataFrame):
    df = pd_all.copy()
    ll = []

    for i in range(720, df.shape[0]):
        if df['ID_LO_3Y'].iloc[i] - df['ID_HI_3Y'].iloc[i] > 480:
            if df['MA50'].iloc[i] > 1.1 * df['MA200'].iloc[i]:
                if df['MA10'].iloc[i] < 1.2 * df['MA200'].iloc[i]:
                    ll.append(pd_all.iloc[i])
    ll = pd.DataFrame(ll)
    ll['filter'] = 'TBKMA200'
    return ll


filter_functions = {
    "_TBKMA200": BKMA200,
}


def eval_lookback_filter(filter_name, data_frame):
    if isinstance(filter_functions[filter_name], str):
        return eval(filter_functions[filter_name])

    return filter_functions[filter_name](data_frame)


def get_indicator_in_report_filter(path="filter.json"):
    def load_custom_filters(path):
        custom_filter = {}
        if os.path.exists(path):
            with open(path, "r") as data:
                custom_filter = json.load(data)
        if 'Init' in custom_filter.keys():
            for key, value in custom_filter.items():
                custom_filter[key] = value.replace("{Init}", custom_filter['Init'])
        return custom_filter

    filters = load_custom_filters(path)
    indicators = []
    for key, value in filters.items():
        if key.startswith('_') or key.startswith('~'):
            indicator = re.findall(r'\b[A-Za-z_]\w*\b', value)
            indicator = [var for var in indicator if not var.isdigit() and var.lower() not in
                         ('and', 'or', 'not', 'if', 'else', 'true', 'false', 'null', 'none', 'in', 'is', 'ge', 'le',
                          'gt', 'lt', 'eq', 'ne', '>=', '<=', '>', '<', '==', '!=', '(', ')', '[', ']', '{', '}',
                          '&', '|', 'abs', 'acos', 'acosh', 'asin', 'asinh', 'atan', 'atanh', 'ceil', 'cos',
                          'cosh')]
            # indicators.append(indicator)
            indicators.extend(indicator)
            # indicator_set = sorted(list(filter(lambda x: x != "time", set(indicator))))
            # indicator_set.insert(0, "time")
            # indicator_set.insert(0, "filter")
            # indicators[key] = indicator_set

    return list(set(indicators))


def export_filter_to_webui_profile():
    dict_filter = json.load(open('filter.json', 'r'))
    dict_filter = json.dumps(dict_filter)
    with open("filter_string.json", "w") as config_file:
        json.dump(dict_filter, config_file)


def parse_and_solve_conditions(expression: str):
    # Step 1: Separate the conditions
    # Separate based on the '&' symbol to get each separate logical condition
    conditions = expression.split('&')
    conditions = [cond.strip() for cond in conditions]

    # Step 2: Prepare variable x and conditions
    x = symbols('x')  # Tạo biến x
    parsed_conditions = []

    for condition in conditions:
        if "x" not in condition:
            continue

        if '>' in condition and '<' not in condition:
            left, right = condition.split('>')
            parsed_conditions.append(eval(f"{left.strip()} > {right.strip()}"))
        elif '<' in condition and '>' not in condition:
            left, right = condition.split('<')
            parsed_conditions.append(eval(f"{left.strip()} < {right.strip()}"))
        elif '>' in condition and '<' in condition:
            parts = condition.split('>')
            left, middle_right = parts[0], parts[1]
            middle, right = middle_right.split('<')
            parsed_conditions.append(eval(f"{left.strip()} < {middle.strip()} < {right.strip()}"))

    # Step 3: Use sympy to solve the conditions simultaneously".
    solutions = []
    for condition in parsed_conditions:
        solution = solve_univariate_inequality(condition, x, relational=False)
        # solution = solve_univariate_inequality(condition, x, relational=True)
        solutions.append(solution)

    combined_solution = solutions[0]
    for solution in solutions[1:]:
        combined_solution = combined_solution.intersection(solution)

    return combined_solution


def revert_indicator_v1(df, expression):
    map_formula = {
        "MA10": "MA10 + (x - Close) / 10",
        "MA50": "MA50 + (x - Close) / 50",
        "MA200": "MA200 + (x - Close) / 200",
        "PE": "PE * (x / Close)",
        "PB": "PB * (x / Close)",
        "PCF": "PCF * (x / Close)",
        "C_H2Y": "C_H2Y * (x / Close)",
        "C_L1W": "C_L1W * (x / Close)",
    }

    if isinstance(df, pd.DataFrame):
        pd_latest = df.iloc[-1]
    else:
        pd_latest = df

    indicator = re.findall(r'\b[A-Za-z_]\w*\b', expression)
    indicator = [var for var in indicator if not var.isdigit() and var.lower() not in
                 ('and', 'or', 'not', 'if', 'else', 'true', 'false', 'null', 'none', 'in', 'is', 'ge', 'le',
                  'gt', 'lt', 'eq', 'ne', '>=', '<=', '>', '<', '==', '!=', '(', ')', '[', ']', '{', '}',
                  '&', '|', 'abs', 'acos', 'acosh', 'asin', 'asinh', 'atan', 'atanh', 'ceil', 'cos',
                  'cosh')]

    indicator_set = sorted(list(set(indicator)) + ['Close'], reverse=True)
    expression = re.sub(r'\bClose\b', 'x', expression)
    expression = expression.replace('=', '')
    for key, formula in map_formula.items():
        if key in indicator_set:
            expression = expression.replace(key, f"({formula})")

    for indicator in indicator_set:
        expression = expression.replace(indicator, str(pd_latest[indicator]))

    combined_solution = parse_and_solve_conditions(expression)
    start = float(combined_solution.args[0]) if combined_solution.args[0] > 0 else 0
    end = float(combined_solution.args[1])

    return [start, end]


class MonitorService:
    def __init__(self, blacklist=None, f_path='../ticker_v1a/'):
        if blacklist is None:
            blacklist = []
        self.blacklist_tickers = blacklist + ["VNINDEX", "VN30", "HNX", "HNX30", "UPCOM"]
        self.fpath = f_path
        self.data_folder = "assets"
        self.ind_df_now = None
        self.ignore_indicators = ['L1W', 'L2W', 'L3W', 'L1M', 'L2M', 'L3M', 'L6M', 'L1Y', 'L2Y', 'H1W', 'H2W', 'H3W',
                                  'H1M', 'H2M', 'H3M', 'H6M', 'H1Y', 'H2Y', 'O1W', 'O2W', 'O3W', 'O1M', 'O2M', 'O3M',
                                  'O6M', 'O1Y', 'O2Y', 'C1W', 'C2W', 'C3W', 'C1M', 'C2M', 'C3M', 'C6M', 'C1Y', 'C2Y',
                                  'Close_1M', 'ID_XVAP1M_Up_P0', 'ID_XVAP1M_Up_P1', 'ID_XVAP1M_Up_P2',
                                  'ID_XVAP1M_Down_P0', 'ID_XVAP1M_Down_P1', 'ID_XVAP1M_Down_P2', 'ID_XVAP3M_Up_P0',
                                  'ID_XVAP3M_Up_P1', 'ID_XVAP3M_Up_P2', 'ID_XVAP3M_Down_P0', 'ID_XVAP3M_Down_P1',
                                  'ID_XVAP3M_Down_P2', 'VNINDEX_PE', 'VNINDEX_LNST', 'Trading_Session',
                                  'Volume_Session', 'VNINDEX_PE_MA2Y', 'VNINDEX_PE_MA4Y', 'VNINDEX_PE_MA5Y',
                                  'ROIC_Min10YCF_OA_3Y', 'DE', 'D_Beta', 'Beta'
                                  ]
        self.used_indicators = list(DICTIONARY.keys())

    def _update_ignore_indicators(self, all_indicators):
        new_ignore_indicators = [i for i in all_indicators if i not in self.used_indicators]
        self.ignore_indicators = list(set(self.ignore_indicators + new_ignore_indicators))

    def monitor(self, observant_tickers, past_time='2024-01-01', n_jobs=20):
        """
        timestart: begin time
        timeend: end time
        :return:
        count null
        percentile 25,50,75,100 data
        """

        def _safe_read_one(ticker, past_time):
            """
            Đọc 1 file CSV của ticker và trả về (past_head1, now_tail1).
            Nếu lỗi thì trả về None để lọc bỏ.
            Dùng hàm riêng vì Pool.map cần 1 callable picklable.
            """
            try:
                path = os.path.join(self.fpath, f"{ticker}.csv")
                df = pd.read_csv(path, dtype={'time': str, 'ticker': str}) \
                    .query(f'time >= "{past_time}"').reset_index(drop=True)

                return (df.head(1), df.tail(1))
            except Exception as e:
                print(f"Error: {ticker} {e}")
                return None



        with Pool(nodes=n_jobs) as pool:
            results = pool.map(_safe_read_one, observant_tickers, [past_time] * len(observant_tickers))

        results = [r for r in results if r is not None]
        past_data = [r[0] for r in results if r[0] is not None and not r[0].empty]
        current_data = [r[1] for r in results if r[1] is not None and not r[1].empty]

        if len(past_data) == 0:
            df_past = pd.DataFrame(columns=['time', 'ticker', 'quarter'])
        else:
            df_past = pd.concat(past_data, axis=0).reset_index(drop=True)

        if len(current_data) == 0:
            df_now = pd.DataFrame(columns=['time', 'ticker', 'quarter'])
        else:
            df_now = pd.concat(current_data, axis=0).reset_index(drop=True)

        # past_data = []
        # current_data = []
        # for ticker in observant_tickers:
        #     try:
        #         df = pd.read_csv(self.fpath + ticker + '.csv', dtype={'time': str, 'ticker': str}) \
        #             .query(f'time>="{past_time}"').reset_index(drop=True)
        #         past_data.append(df.head(1))
        #         current_data.append(df.tail(1))
        #     except Exception as e:
        #         print(f"Error: {ticker} {e}")

        # df_past = pd.concat(past_data, axis=0).reset_index(drop=True)
        # df_now = pd.concat(current_data, axis=0).reset_index(drop=True)

        self.ind_df_now = df_now
        self._update_ignore_indicators(list(set(df_past.columns.tolist() + df_now.columns.tolist())))

        indicator_columns = [col for col in df_past.columns if col not in ['time', 'ticker', 'quarter']]
        # df_1.to_csv("df1.csv", index=False)

        if not os.path.exists(self.data_folder):
            os.mkdir(self.data_folder)
        path = os.path.join(self.data_folder, f"now_df.csv")
        df_now.to_csv(path, index=False)

        ind_df = pd.DataFrame(columns=indicator_columns)

        for col in indicator_columns:
            try:
                data = [
                    True if col in self.ignore_indicators else False,
                    df_past[col].isna().sum(),
                    df_past[col].quantile(0),
                    df_past[col].quantile(0.5),
                    df_past[col].quantile(1),

                    df_now[col].isna().sum(),
                    df_now[col].quantile(0),
                    df_now[col].quantile(0.5),
                    df_now[col].quantile(1),
                ]

                ind_df[col] = pd.Series(data)
            except Exception as e:
                print(f"Error: {col}: {e}")
                ind_df[col] = 0

        today = datetime.today().strftime('%Y-%m-%d')

        ind_df.index = [f'is_ignore_or_removed', f'null_count_{past_time}', f'min_{past_time}', f'q50_{past_time}',
                        f'max_{past_time}', f'null_count_{today}', f'min_{today}', f'q50_{today}', f'max_{today}']

        path = os.path.join(self.data_folder, f"monitor_report.csv")
        ind_df.T.to_csv(path)

        return path

    def null_tickers(self):

        ind_df = self.ind_df_now.copy()

        ignore_drop = [i for i in self.ignore_indicators if i in ind_df.columns]
        ind_df.drop(ignore_drop, axis=1, inplace=True)

        result = {}
        for i, ticker_row in ind_df.iterrows():
            if ticker_row.isna().any():
                null_cols = ticker_row[ticker_row.isna() == True].index.tolist()
                result[ticker_row.ticker] = null_cols

        for ticker in self.blacklist_tickers:
            if ticker in result:
                del (result[ticker])

        result = dict(sorted(result.items()))
        path = os.path.join(self.data_folder, f"null_ticker_monitor.csv")

        df = pd.DataFrame(list(result.items()), columns=['Ticker', 'Indicators'])
        df.to_csv(path)
        return dict(sorted(result.items())), path

    def null_indicators_in_ticker(self, indicators: list):
        """
         This function identifies null indicators in a given list of tickers.

        Parameters:
        indicators (list): A list of indicators to check for null values.

        Returns:
        dict: A dictionary containing tickers with null indicators.
        str: The path to the CSV file where the results are saved.
        """
        indicators = [ind for ind in indicators if ind not in self.ignore_indicators]
        if 'ticker' not in indicators:
            indicators.append('ticker')

        ind_df = self.ind_df_now.loc[:, ~self.ind_df_now.columns.duplicated()]
        ind_df = ind_df[indicators].copy()

        result = {}
        for i, ticker_row in ind_df.iterrows():
            if ticker_row.isna().any():
                null_cols = ticker_row[ticker_row.isna() == True].index.tolist()
                result[ticker_row.ticker] = null_cols

        for ticker in self.blacklist_tickers:
            if ticker in result:
                del (result[ticker])

        result = dict(sorted(result.items()))
        path = os.path.join(self.data_folder, f"necessary_indicators_monitor.csv")

        df = pd.DataFrame(list(result.items()), columns=['Ticker', 'Indicators'])
        df.to_csv(path)
        return dict(sorted(result.items())), path

    def data_render(self):
        pass


if __name__ == "__main__":
    export_filter_to_webui_profile()
    # path = 'assets/MIM_2024-05-20.html'
    # asyncio.get_event_loop().run_until_complete(html2pdf(path))
    # path = '/workspace/kaffa/report/assets/MIM_2024-05-20.html'
    # asyncio.get_event_loop().run_until_complete(html2pdf(path))
    # FPATH = "../ticker_v1a/"
    # processed_tickers = [file.replace('.csv', '') for file in os.listdir(FPATH) if file.endswith('.csv')]

    # minotor = MonitorService(processed_tickers)
    # minotor.monitor()
    # minotor.null_tickers(['HPG'])
    # indicators = get_indicator_in_report_filter()
    # minotor.null_indicators_in_ticker(indicators)
    pass
