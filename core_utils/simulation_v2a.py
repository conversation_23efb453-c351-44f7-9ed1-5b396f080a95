import numpy as np
import pandas as pd

# config = {
#     'initial_amount': 1e7,
#     'cut_loss': 0.3,
#     'ratio_nav': 0.9, # ratio between invest amount and nav (we will not buy more than ratio_nav * nav)
#     'ratio_deal': 0.1, # ratio between each deal amount and invest amount (we will not buy more than ratio_deal * ratio_nav * nav))
#     'ratio_deal_volume': 0.1, # ratio between each deal and daily volume (we will not buy more than ratio_deal_volume * daily_amount )
#     'review_frequency': 'monthly',
#     'fee_buy_rate': 0.001,
#     'fee_sell_rate': 0.002,
#     'score_sell': 0.5,
#     'score_buy': 1,
#     'gamma': 1,
#     'min_ratio_deal_nav': 0.01, # for buy or partial sell
# }

# portfolio:
# * data:
# - cash: float
# - fee_rate: float
# - holdings: ticker, holding_amount, adj_price, init_score, current_score, investment_amount
# - transaction: ymd, ticker, action, buy_amount, sell_amount, adj_price, fee
# * method
# - init(initial_amount)
# - update_ticker(data): update holdings at end of day based on new data(ymd, ticker, adj_price, score)
# - update_holding(data): update holding in the morning based on order plan (ymd, action, ticker, adj_price, amount)


# simulation:
# * data:
# - portfolio: current portfolio
# - config: config of the simulation
# - invest_ratio: float # usually 1. but can be lower based on market condition
# - daily_stats: ymd, nav, cash, list_holding, list_buy, list_sell
# * method
# - init(config): initialize the simulation
# - reset(config=None): reset the simulation
# - set_invest_ratio(invest_ratio): set the invest ratio
# - run(data): main function to run the simulation

class Portfolio:
    def __init__(self, initial_amount, fee_buy_rate, fee_sell_rate):
        self.cash = initial_amount
        self.fee_buy_rate = fee_buy_rate
        self.fee_sell_rate = fee_sell_rate
        self.holdings = {}# dict ['ticker'] of dict { 'holding_amount', 'adj_price', 'init_score', 'current_score', 'investment_amount'}
        self.transactions = [] # list of dict {'ymd', 'ticker', 'action', 'adj_price', 'transaction_amount', 'fee', 'investment_amount'}
        self.log = [] # list of dict {'ymd', 'cash', 'nav', 'num_holdings', 'num_transactions'}

    def update_score_close_price(self, data):
        ''' update holdings at end of day based on new data index by ticker, columns (close_price, score) '''
        for ticker in self.holdings.keys():
            if ticker in data.index:
                try:
                    self.holdings[ticker] = self._update_one_holding(self.holdings[ticker], data.loc[ticker]['close_price'], data.loc[ticker]['score'])
                except:
                    print(f"""update_score_close_price: Warning data not found for {ticker} on {data.index.name}""")
        # sort holdings by current score from low to high
        self.holdings = dict(sorted(self.holdings.items(), key=lambda x: x[1]['current_score'], reverse=False))

    def update_log(self, ymd):
        self.log.append({'ymd': ymd, 'cash': self.cash, 'nav': self.cash + sum(holding['holding_amount'] for holding in self.holdings.values()), 
        'num_holdings': len(self.holdings), 'num_transactions': len(self.transactions)})

    def _update_one_holding(self, holding, new_price,score=None):
        if new_price != holding['adj_price']:
            holding['holding_amount'] = holding['holding_amount'] * new_price / holding['adj_price']
            holding['adj_price'] = new_price
            if score is not None:
                holding['current_score'] = score
        return holding

    def execute_plan(self, ymd, plans):
        ''' update holding in the morning based on order plan - list of dict (action, ticker, amount, score, adj_price) 
        the holding might not be updated with latest price
        the plans should be updated with latest price
        '''
        if plans is None:
            return
        cnt = 0

        # step-by-step SELL:
        # - compute transaction amount and fee
        # - update holding list
        # - update cash
        # - append transaction
        for plan in plans:
            ticker = plan['ticker']
            if plan['action']!='sell':
                continue
            # if amount is larger than holding, sell all
            if ticker not in self.holdings:
                print(f"""execute_plan: Warning ticker not in holdings: {ticker}""")
                continue
            # update adj_price, amount of the holding
            self.holdings[ticker] = self._update_one_holding(self.holdings[ticker], plan['adj_price'])

            transaction_amount = min(plan['amount'], self.holdings[ticker]['holding_amount'])
            fee = transaction_amount * self.fee_sell_rate
            # investment amount corresponds to the amount of the transaction
            original_buy_amount = self.holdings[ticker]['investment_amount'] * transaction_amount / self.holdings[ticker]['holding_amount']
            self.cash += (transaction_amount - fee)

            # append transaction
            self.transactions += [{'ymd': ymd, 
                                  'ticker': ticker, 
                                  'action': 'sell', 
                                  'adj_price': plan['adj_price'], 
                                  'buy_amount': original_buy_amount, 
                                  'sell_amount': transaction_amount, 
                                  'fee': fee}]
            cnt += 1

            self.holdings[ticker]['holding_amount'] -= transaction_amount
            self.holdings[ticker]['investment_amount'] -= original_buy_amount
            if self.holdings[ticker]['holding_amount'] <= 10000.0:
                del self.holdings[ticker]
        
        # step-by-step BUY:
        # - compute transaction amount and fee
        # - update holding
        # - update cash
        # - append transaction
        for plan in plans:
            if plan['action']!='buy':
                continue
            # print(f"""Plan amount: {plan['amount']}
            # Cash: {self.cash}
            # Fee buy rate: {self.fee_buy_rate}
            # """)
            transaction_amount = min(plan['amount'], self.cash * (1.0 - self.fee_buy_rate))
            fee = transaction_amount * self.fee_buy_rate

            if plan['ticker'] in self.holdings:
                self.holdings[plan['ticker']]['holding_amount'] += transaction_amount
                self.holdings[plan['ticker']]['investment_amount'] += transaction_amount
            else:
                self.holdings[plan['ticker']] = {'holding_amount': transaction_amount,
                                         'adj_price': plan['adj_price'],
                                         'init_score': plan['score'],
                                         'current_score': plan['score'],
                                         'investment_amount': transaction_amount}
            self.cash -= (transaction_amount+fee)
            self.transactions += [{'ymd': ymd, 
                                'ticker': ticker, 
                                'action': 'buy', 
                                'adj_price': plan['adj_price'], 
                                'buy_amount': transaction_amount, 
                                'sell_amount': 0, 
                                'fee': fee}]
            cnt += 1
        if cnt != len(plans):
            print(f"""execute_plan: Warning cnt != len(plans) cnt: {cnt} len(plans): {len(plans)}""")

class Simulation:
    def __init__(self, config):
        self.portfolio = Portfolio(initial_amount=config['initial_amount'], fee_buy_rate=config['fee_buy_rate'], fee_sell_rate=config['fee_sell_rate'])
        self.config = config
        self.invest_ratio = 1

    def set_invest_ratio(self, invest_ratio):
        if (invest_ratio > 1) or (invest_ratio < 0):
            raise ValueError('invest_ratio must be between 0 and 1')
        self.invest_ratio = invest_ratio
        return self

    def reset(self,config=None):
        if config is None:
            config = self.config
        self.portfolio = Portfolio(initial_amount=config['initial_amount'], fee_buy_rate=config['fee_buy_rate'], fee_sell_rate=config['fee_sell_rate'])
        self.config = config

    def run(self, data):
        ''' Run the simulation
        Input: dataframe with columns: ymd, ticker, open_price, close_price, score, daily_amount
        Output: None
        The simulation will be run for each day in the input data.
        '''
        pdx = data.set_index('ymd')
        ymd_list = sorted(pdx.index.unique())
        plans=None
        for ymd in ymd_list:
            # print(ymd)
#                     =data.set_index('ymd').loc['2018-10-22'].reset_index(drop=True).head(2)
            xx = pdx.loc[ymd].reset_index(drop=True)
            data_ymd = xx.set_index('ticker').sort_values('score',ascending=False)
            plans = self.run_day(ymd,data_ymd, plans)
            self.portfolio.update_log(ymd)
    
    def run_day(self, ymd, data, plans=None):
        ''' Run the simulation for a single day
        Input: 
        - data: dataframe (ticker, open_price, close_price, score, daily_amount)
        - plans: list of dict (action, ticker, amount)
        Output:
        - new plans: list of dict (action, ticker, amount, adj_price)
        Process:
        - update price and score of holding tickers
        - execute plans
        - prepare order plan for the next day
        '''
        # update plan adj_price with data.
        # print(f"""run {ymd}
        # plans: {plans}""")
        if plans is not None:
            for plan in plans:
                try:
                    # for sell, update sell amount with open price
                    if plan['action']=='sell':
                        plan['amount'] = plan['amount'] * data.loc[plan['ticker'], 'open_price'] / plan['adj_price']
                    # for both, update adj_price with open price
                    plan['adj_price'] = data.loc[plan['ticker'], 'open_price']
                except:
                    print(f"""run_day: Warning data not found for {plan['ticker']} on {ymd}""")
        # execute plan                
        self.portfolio.execute_plan(ymd, plans) # sell and buy. Note: we should use the updated adj_price in plans (since holding does not necessarily has the adjusted price for all tickers)=
        # update ticker with score and close price
        self.portfolio.update_score_close_price(data)
        # prepare order plan for the next day
        new_plans = self.prepare_order_plan(data)
        if len(new_plans) > 0:
            print(f"""{ymd} nav(mil)={(self.portfolio.cash + sum(holding['holding_amount'] for holding in self.portfolio.holdings.values()))/1e+6:.2f} cash: {self.portfolio.cash:.2f}
            new_plans: {new_plans} holdings={len(self.portfolio.holdings)}""")
            #display(pd.DataFrame(self.portfolio.holdings))
        return new_plans


    def _find_potential_swaps(self,target_score, expected_amount, cash, min_buy_amount, invest):
        swaps = {}
        acc_amount = cash
        for ticker, e in invest.items():
            if e['score'] <= target_score:
                if acc_amount + e['amount'] > expected_amount:
                    swaps[ticker] =  (acc_amount - expected_amount) # negative amount means sell
                    acc_amount = expected_amount
                    break
                else:
                    swaps[ticker] = -e['amount']
                    acc_amount += e['amount']
            else:
                break
        if acc_amount >= min_buy_amount:
            swaps[ticker] = acc_amount # positive amount means buy
        else:
            swaps = {}
        return swaps

    def prepare_order_plan(self, data):
        ''' Prepare order plan for the next day
        Input:
            - ymd: ymd
            - data (ticker, open_price, close_price, score, daily_amount), sorted by score from high to low
            - self.portfolio: current portfolio with holdings (ticker | holding_amount, adj_price, init_score, current_score, investment_amount) and cash

        Output: list of dict (action, ticker, amount)
        Process:
        Optimization problem: 
        maximize sum_i invest_i * score_i 
               - |invest_i - holding_i|*0.5*gamma
        st. invest_i = 0 if score_i < score_sell
            invest_i-holding_i = 0 if score_i < score_buy
            invest_i < max_amount_i
            if invest_i-holding_i > 0:
                (invest_i-holding_i >= min_buy_amount)
            sum(invest_i) <= nav * invest_ratio
            invest_i >= 0
        Input/Argument: 
        - holding_i      : current holding amount of ticker i=1..N
        - score_i        : score of ticker i=1..N
        - max_amount_i   : max amount of ticker i=1..N (capped by daily_amount and invest_amount and ratio_ticker)
        - cash           : available cash
        - config.min_ratio_deal_nav : min deal ratio to nav
#        -> min_buy_amount  = min_ratio_deal_nav* nav # min buying amount of a transaction (for complete buy or partial sell)
        - config.invest_ratio   : invest ratio
        - config.score_sell     : score to sell
        - config.score_buy      : score to buy
        - config.gamma          : default = 1 (score diff)

        Note: 
        - if we sell a ticker, will we sell all? 
        -- Situation: holding j with 0.2 nav , potential i with 0.01 nav
        -- Will we sell all j, to buy i?
        - buy and sell at the same time?
            score is still high but hit cut_loss -> check cut_loss and update score
        

        
        Step-by-step: compute optimal invest amount for each ticker
        - init: invest_i = holding_i
        - sell full tickers with current_score < score_sell (and update cash)
        - sell partial tickers if holding amount > expected amount (nav) + min_ratio_deal_nav * nav * 0.5 # we allow the ticker to gain 0.5 slot. If we sell, then delta = sell holding - expected amount
        - find change to buy good ticker:
        - loop over top ticker i with score larger than lowest holding
        -- compute expected amount, and delta = expected - holding
        -- if delta < min_buy_amount, then skip
        -- if (cash >= delta), then buy the difference, and update cash
        -- if (cash <= delta), then find potential swaps to make
        -- If total swap + cash >= min_buy_amount, then make sell until cash>delta or no more swap, and buy ticker i with amount min(delta,cash)


        

        def find_potential_swap(score_i, cash, min_buy_amount, gamma, holdings)
        - loop over holding j from lowest to highest
        -- if (score_i - score_j > gamma), then mark j as potential swap, if total_swap + cash >= min_buy_amount, then break
        -- else break
        - output: list of potential swaps (ticker_j, amount_j)

        '''

        invest = {ticker: {'score': holding['current_score'], 
                           'amount':holding['holding_amount']} for ticker, holding in self.portfolio.holdings.items()}
        cash = self.portfolio.cash
        nav = self.portfolio.cash + sum(holding['holding_amount'] for holding in self.portfolio.holdings.values())
        min_buy_amount = self.config['min_ratio_deal_nav'] * nav
        slot_amount = nav * self.config['ratio_nav'] * self.config['ratio_deal']
        gamma = self.config['gamma']
        score_sell = self.config['score_sell']
        score_buy = self.config['score_buy']
        ratio_deal_volume = self.config['ratio_deal_volume']

        # print(f"""=== Prepare order plan ===
        # invest: {invest}
        # cash: {cash}
        # nav: {nav}
        # min_buy_amount: {min_buy_amount}
        # slot_amount: {slot_amount}
        # gamma: {gamma}
        # """)
#        display(data.head(10))

        # sell full tickers with current_score < score_sell
        # sell partial tickers if holding amount > expected amount + 0.5 slot
#        to_del = []
        for ticker, e in invest.items():
            if e['score'] <= score_sell:
                cash += e['amount']
                invest[ticker]['amount'] = 0
 #               to_del.append(ticker)
            else:
                expected_amount = slot_amount * self.score_to_ratio_nav(e['score'],score_buy,gamma)
                if e['amount'] > expected_amount + slot_amount * 0.5:
                    invest[ticker] = {'score': e['score'], 'amount': expected_amount}
                    cash += (e['amount'] - expected_amount)
  #      for ticker in to_del:
  #          del invest[ticker]

        # buy tickers with cash then swaps score > lowest score + gamma
        for ticker in data.index:
            if data.loc[ticker]['score'] < score_buy:
                break # the ticker is not good enough to buy
            expected_amount = min(slot_amount * self.score_to_ratio_nav(data.loc[ticker]['score'],score_buy,gamma), data.loc[ticker]['daily_amount'] * ratio_deal_volume,cash)
            current_amount = 0 if ticker not in invest else invest[ticker]['amount']
            delta = expected_amount - current_amount

            if delta < min_buy_amount:
                continue # 

            if cash >= delta:
                invest[ticker] = {'score': data.loc[ticker]['score'], 'amount': expected_amount}
                cash -= delta
                continue

            # cash is not enough to buy the expected amount, then buy partial amount and find swaps
            # check to swap
            # if len(invest) == 0:
                # continue # no ticker to swap
            # if data.loc[ticker]['score'] - invest[invest.keys()[0]]['score'] < gamma:
            #     break # the gap between best and worst ticker is not enough

            swaps = self._find_potential_swaps(data.loc[ticker]['score']-gamma, delta, cash, min_buy_amount, invest)

            # update invest
#            to_del = []
            for tt in swaps.keys():
                if tt in invest:
                    invest[tt]['amount'] = float(int(invest[tt]['amount'] + swaps[tt]))
                    cash -= swaps[tt]
#                    if invest[tt]['amount'] == 0:
#                        to_del.append(tt)
                else:
                    invest[tt] = {'score': data.loc[tt]['score'], 'amount': swaps[tt]}
                    cash -= swaps[tt]
#            for tt in to_del:
#                del invest[tt]
        plans = []
        for ticker in invest.keys():
            if ticker in self.portfolio.holdings:
                prev_amount = self.portfolio.holdings[ticker]['holding_amount']
            else:
                prev_amount = 0
            delta = invest[ticker]['amount'] - prev_amount
            if int(delta) != 0:
                if delta > 0:
                    plans.append({'action': 'buy', 'ticker': ticker, 'score': invest[ticker]['score'], 'adj_price': data.loc[ticker]['close_price'], 'amount': delta})
                else:
                    plans.append({'action': 'sell', 'ticker': ticker, 'score': invest[ticker]['score'], 'adj_price': data.loc[ticker]['close_price'], 'amount': -delta})
        return plans

    def get_stats(self):
        return {
            'cash': self.portfolio.cash,
            'nav': self.portfolio.cash + sum(holding['holding_amount'] for holding in self.portfolio.holdings.values()),
            'num_transactions': len(self.portfolio.transactions),
            'num_holdings': len(self.portfolio.holdings),
        }
        

    def score_to_ratio_nav(self, score,score_buy,gamma):
        if score>=score_buy+gamma*2:
            return 3
        elif score>=score_buy+gamma:
            return 2
        elif score>=score_buy:
            return 1
        else:
            return 0


# Usage:
if True:
    #exec(open('simulation_v2a.py').read())
    config = {
        'initial_amount': 1e9,
        'cut_loss': 0.3,
        'ratio_nav': 1.0, # ratio between invest amount and nav (we will not buy more than ratio_nav * nav)
        'ratio_deal': 0.1, # ratio between each deal amount and invest amount (we will not buy more than ratio_deal * ratio_nav * nav))
        'ratio_deal_volume': 0.1, # ratio between each deal and daily volume (we will not buy more than ratio_deal_volume * daily_volume * price_target)
        'review_frequency': 'monthly',
        'fee_buy_rate': 0.001,
        'fee_sell_rate': 0.002,
        'score_sell': 0,
        'score_buy': 1,
        'gamma': 1,
        'min_ratio_deal_nav': 0.01, # for buy or partial sell
    }

    ## load data (ymd,ticker, open_price, close_price, score, daily_amount)
    pdX = pd.read_csv('test_weight_score.csv')
    pdX['time'] = pd.to_datetime(pdX['time'], format='%m/%d/%Y')
    pdX['ymd']=pdX['time'].dt.strftime('%Y-%m-%d')

    ii = (pdX['close']>0)&(pdX['price']>0)&(pdX['volume_p50_1m']>0)&(pdX['score'].notnull()&(pdX['ymd']>="2014-01-01"))
    data = pdX[ii][['ymd', 'ticker', 'close','price', 'score', 'volume_p50_1m']].copy().rename(columns={'close': 'close_price'})
    data['open_price'] = data['close_price']
    data['daily_amount'] = data['volume_p50_1m'] * data['price']
    data.drop(columns=['volume_p50_1m', 'price'], inplace=True)

    sim = Simulation(config)
    sim.run(data[data['ymd'] < '2024-02-05'])
    sim.get_stats()
